/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 链接样式 */
a {
  color: var(--el-color-primary);
  text-decoration: none;
  transition: color 0.3s;
}

a:hover {
  color: var(--el-color-primary-light-3);
}

/* 按钮样式增强 */
.el-button {
  border-radius: 8px;
  font-weight: 500;
}

.el-button--primary {
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, var(--el-color-primary-light-3) 0%, var(--el-color-primary-light-5) 100%);
}

/* 卡片样式 */
.content-card {
  background: var(--el-bg-color);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.content-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 页面标题 */
.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 24px;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-center { display: flex; align-items: center; justify-content: center; }
.flex-between { display: flex; align-items: center; justify-content: space-between; }
.flex-column { display: flex; flex-direction: column; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.mb-4 { margin-bottom: 16px; }
.mb-6 { margin-bottom: 24px; }
.mt-4 { margin-top: 16px; }
.mt-6 { margin-top: 24px; }

.p-4 { padding: 16px; }
.p-6 { padding: 24px; }

/* 响应式工具类 */
@media (max-width: 768px) {
  .mobile-hidden { display: none !important; }
  .mobile-full { width: 100% !important; }
}

@media (min-width: 769px) {
  .desktop-hidden { display: none !important; }
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--el-color-primary-light-8);
  border-radius: 50%;
  border-top-color: var(--el-color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 内容区域 */
.content-area {
  min-height: calc(100vh - 120px);
  padding: 20px 0;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--el-text-color-secondary);
}

.empty-state .empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state .empty-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.empty-state .empty-description {
  font-size: 14px;
  color: var(--el-text-color-placeholder);
}

/* 标签样式 */
.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.tag-list .el-tag {
  border-radius: 16px;
  font-size: 12px;
  padding: 4px 12px;
}

/* 价格显示 */
.price-display {
  color: var(--el-color-danger);
  font-weight: 600;
  font-size: 18px;
}

.price-display .currency {
  font-size: 14px;
  margin-right: 2px;
}

/* VIP标识 */
.vip-badge {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #fff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 解锁状态 */
.unlock-status {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.unlock-status.unlocked {
  background: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.unlock-status.locked {
  background: var(--el-color-warning-light-9);
  color: var(--el-color-warning);
}

/* 统计数字 */
.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-color-primary);
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

/* 头像样式 */
.user-avatar {
  border-radius: 50%;
  border: 2px solid var(--el-border-color-lighter);
  transition: border-color 0.3s;
}

.user-avatar:hover {
  border-color: var(--el-color-primary);
}
