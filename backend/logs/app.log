2025-07-06 13:42:14 [INFO]: 🚀 服务器启动成功！
2025-07-06 13:42:14 [INFO]: 📍 地址: http://0.0.0.0:3000
2025-07-06 13:42:14 [INFO]: 🌍 环境: undefined
2025-07-06 13:42:14 [INFO]: 📚 API文档: http://0.0.0.0:3000/api/v1/docs
2025-07-06 13:42:36 [INFO]: GET / - 200 - 1ms
2025-07-06 13:42:44 [INFO]: GET /api/v1/public/home - 200 - 0ms
2025-07-06 13:46:39 [INFO]: GET /api/v1/public/categories - 200 - 0ms
2025-07-06 13:46:47 [INFO]: GET /api/v1/public/stats - 200 - 0ms
2025-07-06 13:53:37 [INFO]: GET /api/v1/public/config?_t=1751781216767 - 200 - 0ms
2025-07-06 13:53:39 [INFO]: GET /api/v1/public/config?_t=1751781219276 - 200 - 0ms
2025-07-06 13:53:40 [INFO]: GET /api/v1/public/home?_t=1751781220780 - 200 - 0ms
2025-07-06 13:53:40 [INFO]: GET /api/v1/public/stats?_t=1751781220780 - 200 - 0ms
2025-07-06 13:54:43 [INFO]: GET /api/v1/public/home?_t=1751781283090 - 200 - 0ms
2025-07-06 13:54:43 [INFO]: GET /api/v1/public/stats?_t=1751781283090 - 200 - 0ms
2025-07-06 14:14:57 [INFO]: 🚀 服务器启动成功！
2025-07-06 14:14:57 [INFO]: 📍 地址: http://0.0.0.0:3000
2025-07-06 14:14:57 [INFO]: 🌍 环境: undefined
2025-07-06 14:14:57 [INFO]: 📚 API文档: http://0.0.0.0:3000/api/v1/docs
2025-07-06 14:15:18 [INFO]: GET /api/v1/public/home - 200 - 1ms
2025-07-06 14:15:27 [INFO]: GET /api/v1/public/categories - 200 - 0ms
2025-07-06 14:21:58 [INFO]: 🚀 服务器启动成功！
2025-07-06 14:21:58 [INFO]: 📍 地址: http://0.0.0.0:3000
2025-07-06 14:21:58 [INFO]: 🌍 环境: undefined
2025-07-06 14:21:58 [INFO]: 📚 API文档: http://0.0.0.0:3000/api/v1/docs
2025-07-06 15:04:58 [INFO]: 🔗 连接到MySQL服务器...
2025-07-06 15:04:58 [ERROR]: ❌ 数据库初始化失败:
Error: connect ECONNREFUSED 127.0.0.1:3306
    at Object.createConnectionPromise [as createConnection] (/root/zhis/backend/node_modules/mysql2/promise.js:19:31)
    at initDatabase (/root/zhis/backend/database/init-database.js:20:30)
    at Object.<anonymous> (/root/zhis/backend/database/init-database.js:120:3)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
    at node:internal/main/run_main_module:28:49
2025-07-06 15:06:06 [INFO]: 🔗 连接到MySQL服务器...
2025-07-06 15:06:06 [ERROR]: ❌ 数据库初始化失败:
Error: Access denied for user 'root'@'192.168.31.134' (using password: YES)
    at Object.createConnectionPromise [as createConnection] (/root/zhis/backend/node_modules/mysql2/promise.js:19:31)
    at initDatabase (/root/zhis/backend/database/init-database.js:20:30)
    at Object.<anonymous> (/root/zhis/backend/database/init-database.js:120:3)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
    at node:internal/main/run_main_module:28:49
2025-07-06 15:14:16 [INFO]: 🔗 连接到MySQL服务器...
2025-07-06 15:14:16 [ERROR]: ❌ 数据库初始化失败:
Error: Access denied for user 'root'@'localhost'
    at Object.createConnectionPromise [as createConnection] (/root/zhis/backend/node_modules/mysql2/promise.js:19:31)
    at initDatabase (/root/zhis/backend/database/init-database.js:20:30)
    at Object.<anonymous> (/root/zhis/backend/database/init-database.js:120:3)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
    at node:internal/main/run_main_module:28:49
2025-07-06 15:14:48 [INFO]: 🔗 连接到MySQL服务器...
2025-07-06 15:14:48 [INFO]: 📦 创建数据库...
2025-07-06 15:14:48 [INFO]: ✅ 数据库 zhis 创建成功
2025-07-06 15:14:48 [ERROR]: ❌ 数据库初始化失败:
Error: This command is not supported in the prepared statement protocol yet
    at PromiseConnection.execute (/root/zhis/backend/node_modules/mysql2/lib/promise/connection.js:47:22)
    at initDatabase (/root/zhis/backend/database/init-database.js:29:22)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-06 15:15:30 [INFO]: 🔗 连接到MySQL服务器...
2025-07-06 15:15:30 [INFO]: 📦 创建数据库...
2025-07-06 15:15:30 [INFO]: ✅ 数据库 zhis 创建成功
2025-07-06 15:15:30 [ERROR]: ❌ 数据库初始化失败:
Error: This command is not supported in the prepared statement protocol yet
    at PromiseConnection.execute (/root/zhis/backend/node_modules/mysql2/lib/promise/connection.js:47:22)
    at initDatabase (/root/zhis/backend/database/init-database.js:29:22)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-06 15:15:52 [INFO]: 🔗 连接到MySQL服务器...
2025-07-06 15:15:52 [INFO]: 📦 创建数据库...
2025-07-06 15:15:52 [INFO]: ✅ 数据库 zhis 创建成功
2025-07-06 15:15:52 [INFO]: 📋 执行数据库架构脚本...
2025-07-06 15:15:55 [INFO]: ✅ 数据库表创建成功
2025-07-06 15:15:55 [INFO]: 🌱 插入初始数据...
2025-07-06 15:15:55 [INFO]: ✅ 初始数据插入成功
2025-07-06 15:16:56 [INFO]: 开始数据库功能测试...
2025-07-06 15:16:56 [INFO]: 测试数据库连接...
2025-07-06 15:16:56 [INFO]: ✅ 数据库连接测试成功
2025-07-06 15:16:56 [INFO]: 测试基本查询...
2025-07-06 15:16:56 [INFO]: 数据库中有 22 个表
2025-07-06 15:16:56 [INFO]: 测试分类数据...
2025-07-06 15:16:56 [INFO]: 分类表中有 6 条记录
2025-07-06 15:16:56 [INFO]: 测试管理员数据...
2025-07-06 15:16:56 [INFO]: 管理员表中有 1 条记录
2025-07-06 15:16:56 [INFO]: 默认管理员: admin (超级管理员)
2025-07-06 15:16:56 [INFO]: 测试积分配置...
2025-07-06 15:16:56 [INFO]: 积分配置表中有 6 条记录
2025-07-06 15:16:56 [INFO]: 测试插入操作...
2025-07-06 15:16:56 [INFO]: 插入测试用户成功，ID: 1
2025-07-06 15:16:56 [INFO]: 查询测试用户成功: 测试用户
2025-07-06 15:16:56 [INFO]: 清理测试数据完成
2025-07-06 15:33:21 [INFO]: 开始测试认证系统...
2025-07-06 15:33:21 [INFO]: 测试1: 创建测试用户
2025-07-06 15:33:21 [ERROR]: ❌ 认证系统测试失败:
Error
    at Query.run (/root/zhis/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
    at /root/zhis/backend/node_modules/sequelize/lib/sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.insert (/root/zhis/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:308:21)
    at async model.save (/root/zhis/backend/node_modules/sequelize/lib/model.js:2490:35)
    at async User.create (/root/zhis/backend/node_modules/sequelize/lib/model.js:1362:12)
    at async testAuthSystem (/root/zhis/backend/test-auth-internal.js:13:22)
2025-07-06 15:35:35 [INFO]: 开始测试认证系统...
2025-07-06 15:35:35 [INFO]: 测试1: 创建测试用户
2025-07-06 15:35:35 [ERROR]: ❌ 认证系统测试失败:
Error
    at Query.run (/root/zhis/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
    at /root/zhis/backend/node_modules/sequelize/lib/sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.insert (/root/zhis/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:308:21)
    at async model.save (/root/zhis/backend/node_modules/sequelize/lib/model.js:2490:35)
    at async User.create (/root/zhis/backend/node_modules/sequelize/lib/model.js:1362:12)
    at async testAuthSystem (/root/zhis/backend/test-auth-internal.js:13:22)
2025-07-06 15:40:32 [INFO]: 🚀 启动微信知识付费平台后端服务...
2025-07-06 15:40:32 [INFO]: 开始初始化数据库...
2025-07-06 15:40:32 [INFO]: 数据库连接成功
2025-07-06 15:40:32 [ERROR]: 数据库同步失败
Error
    at Query.run (/root/zhis/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
    at /root/zhis/backend/node_modules/sequelize/lib/sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.addIndex (/root/zhis/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:250:12)
    at async User.sync (/root/zhis/backend/node_modules/sequelize/lib/model.js:999:7)
    at async Sequelize.sync (/root/zhis/backend/node_modules/sequelize/lib/sequelize.js:377:9)
    at async syncDatabase (/root/zhis/backend/src/models/index.js:176:5)
    at async initDatabase (/root/zhis/backend/database/init.js:14:5)
    at async start (/root/zhis/backend/scripts/start.js:9:5)
2025-07-06 15:41:09 [INFO]: 🚀 启动微信知识付费平台后端服务...
2025-07-06 15:41:09 [INFO]: 开始初始化数据库...
2025-07-06 15:41:09 [INFO]: 数据库连接成功
2025-07-06 15:41:10 [ERROR]: 数据库同步失败
Error
    at Query.run (/root/zhis/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
    at /root/zhis/backend/node_modules/sequelize/lib/sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.addIndex (/root/zhis/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:250:12)
    at async User.sync (/root/zhis/backend/node_modules/sequelize/lib/model.js:999:7)
    at async Sequelize.sync (/root/zhis/backend/node_modules/sequelize/lib/sequelize.js:377:9)
    at async syncDatabase (/root/zhis/backend/src/models/index.js:176:5)
    at async initDatabase (/root/zhis/backend/database/init.js:14:5)
    at async start (/root/zhis/backend/scripts/start.js:9:5)
2025-07-06 15:42:43 [INFO]: 🚀 启动微信知识付费平台后端服务...
2025-07-06 15:42:43 [INFO]: 开始初始化数据库...
2025-07-06 15:42:43 [INFO]: 数据库连接成功
2025-07-06 15:42:44 [ERROR]: 数据库同步失败
Error
    at Query.run (/root/zhis/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
    at /root/zhis/backend/node_modules/sequelize/lib/sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.addIndex (/root/zhis/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:250:12)
    at async Content.sync (/root/zhis/backend/node_modules/sequelize/lib/model.js:999:7)
    at async Sequelize.sync (/root/zhis/backend/node_modules/sequelize/lib/sequelize.js:377:9)
    at async syncDatabase (/root/zhis/backend/src/models/index.js:176:5)
    at async initDatabase (/root/zhis/backend/database/init.js:14:5)
    at async start (/root/zhis/backend/scripts/start.js:9:5)
2025-07-06 15:45:08 [INFO]: 🚀 测试服务器启动成功，端口: 3000
2025-07-06 15:45:08 [INFO]: 📱 微信扫码登录演示页面: http://localhost:3000/wechat-qr-login.html
2025-07-06 15:45:31 [INFO]: 开始测试微信扫码登录功能...
2025-07-06 15:45:31 [INFO]: 测试1: 生成微信扫码登录二维码
2025-07-06 15:45:34 [ERROR]: ❌ 测试失败:
2025-07-06 15:45:34 [INFO]: 测试微信扫码登录回调处理...
2025-07-06 15:45:37 [INFO]: ⚠️  回调接口返回错误（预期的，因为使用了模拟数据）
2025-07-06 15:45:50 [INFO]: 生成微信扫码登录二维码
2025-07-06 16:00:12 [INFO]: 开始测试内容管理功能...
2025-07-06 16:00:12 [INFO]: 测试1: 获取分类列表
2025-07-06 16:00:14 [ERROR]: ❌ 获取分类列表失败: Request failed with status code 502
2025-07-06 16:00:14 [INFO]: 测试2: 获取内容列表
2025-07-06 16:00:16 [ERROR]: ❌ 获取内容列表失败: Request failed with status code 502
2025-07-06 16:00:16 [INFO]: 测试3: 搜索内容
2025-07-06 16:00:18 [ERROR]: ❌ 搜索功能失败: Request failed with status code 502
2025-07-06 16:00:18 [INFO]: 测试4: 创建测试内容
2025-07-06 16:00:21 [ERROR]: ❌ 创建内容失败: Request failed with status code 502
2025-07-06 16:00:21 [INFO]: 💡 提示: 创建内容需要有效的管理员令牌和数据库连接
2025-07-06 16:00:21 [INFO]: 测试9: 分类管理功能
2025-07-06 16:00:25 [ERROR]: ❌ 分类管理测试失败: Request failed with status code 502
2025-07-06 16:01:08 [INFO]: 开始测试内容管理功能...
2025-07-06 16:01:08 [INFO]: 测试1: 获取分类列表
2025-07-06 16:01:11 [ERROR]: ❌ 获取分类列表失败: Request failed with status code 502
2025-07-06 16:01:11 [INFO]: 测试2: 获取内容列表
2025-07-06 16:01:13 [ERROR]: ❌ 获取内容列表失败: Request failed with status code 502
2025-07-06 16:01:13 [INFO]: 测试3: 搜索内容
2025-07-06 16:01:16 [ERROR]: ❌ 搜索功能失败: Request failed with status code 502
2025-07-06 16:01:16 [INFO]: 测试4: 创建测试内容
2025-07-06 16:01:19 [ERROR]: ❌ 创建内容失败: Request failed with status code 502
2025-07-06 16:01:19 [INFO]: 💡 提示: 创建内容需要有效的管理员令牌和数据库连接
2025-07-06 16:01:19 [INFO]: 测试9: 分类管理功能
2025-07-06 16:01:22 [ERROR]: ❌ 分类管理测试失败: Request failed with status code 502
2025-07-06 16:02:38 [INFO]: 🚀 测试服务器启动成功，端口: 3000
2025-07-06 16:02:38 [INFO]: 📱 微信扫码登录演示页面: http://localhost:3000/wechat-qr-login.html
2025-07-06 16:02:59 [INFO]: 开始测试内容管理功能...
2025-07-06 16:02:59 [INFO]: 测试1: 获取分类列表
2025-07-06 16:03:02 [ERROR]: ❌ 获取分类列表失败: Request failed with status code 502
2025-07-06 16:03:02 [INFO]: 测试2: 获取内容列表
2025-07-06 16:03:05 [ERROR]: ❌ 获取内容列表失败: Request failed with status code 502
2025-07-06 16:03:05 [INFO]: 测试3: 搜索内容
2025-07-06 16:03:08 [ERROR]: ❌ 搜索功能失败: Request failed with status code 502
2025-07-06 16:03:08 [INFO]: 测试4: 创建测试内容
2025-07-06 16:03:11 [ERROR]: ❌ 创建内容失败: Request failed with status code 502
2025-07-06 16:03:11 [INFO]: 💡 提示: 创建内容需要有效的管理员令牌和数据库连接
2025-07-06 16:03:11 [INFO]: 测试9: 分类管理功能
2025-07-06 16:03:13 [ERROR]: ❌ 分类管理测试失败: Request failed with status code 502
2025-07-06 16:08:22 [INFO]: 开始测试内容管理功能...
2025-07-06 16:08:22 [INFO]: 测试1: 获取分类列表
2025-07-06 16:08:22 [INFO]: ✅ 获取分类列表成功，共 3 个分类
2025-07-06 16:08:22 [INFO]:   - 技术文章 (ID: 1, Slug: tech)
2025-07-06 16:08:22 [INFO]:   - 生活分享 (ID: 2, Slug: life)
2025-07-06 16:08:22 [INFO]:   - 学习资源 (ID: 3, Slug: study)
2025-07-06 16:08:22 [INFO]: 测试2: 获取内容列表
2025-07-06 16:08:22 [INFO]: ✅ 获取内容列表成功，共 2 个内容
2025-07-06 16:08:22 [INFO]:   - 示例文章1 (ID: 1, 类型: 文章)
2025-07-06 16:08:22 [INFO]:   - 付费内容示例 (ID: 2, 类型: 文章)
2025-07-06 16:08:22 [INFO]: 测试3: 搜索内容
2025-07-06 16:08:22 [INFO]: ✅ 搜索功能正常，找到 2 个结果
2025-07-06 16:08:22 [INFO]: 测试4: 创建测试内容
2025-07-06 16:08:22 [INFO]: ✅ 创建内容成功，ID: 3
2025-07-06 16:08:22 [INFO]: 测试5: 获取内容详情
2025-07-06 16:08:22 [INFO]: ✅ 获取内容详情成功: 测试内容 - 1751789302157
2025-07-06 16:08:22 [INFO]:   - 浏览次数: 1
2025-07-06 16:08:22 [INFO]:   - 解锁状态: 已解锁
2025-07-06 16:08:22 [INFO]: 测试6: 检查解锁状态
2025-07-06 16:08:22 [INFO]: ✅ 检查解锁状态成功: 已解锁
2025-07-06 16:08:22 [INFO]: 测试7: 更新内容
2025-07-06 16:08:22 [INFO]: ✅ 更新内容成功
2025-07-06 16:08:22 [INFO]: 测试8: 删除内容
2025-07-06 16:08:22 [INFO]: ✅ 删除内容成功
2025-07-06 16:08:22 [INFO]: 测试9: 分类管理功能
2025-07-06 16:08:22 [INFO]: ✅ 创建分类成功，ID: 4
2025-07-06 16:08:22 [INFO]: ✅ 删除测试分类成功
2025-07-06 16:09:23 [INFO]: 开始测试内容解锁功能的各种场景...
2025-07-06 16:09:23 [INFO]: 测试1: 免费内容解锁
2025-07-06 16:09:23 [INFO]: ✅ 创建免费内容成功，ID: 4
2025-07-06 16:09:23 [INFO]: ✅ 免费内容解锁状态检查: 已解锁
2025-07-06 16:09:23 [INFO]: ✅ 免费内容详情获取成功: 免费内容测试 - 1751789363153
2025-07-06 16:09:23 [INFO]:   - 解锁状态: 已解锁
2025-07-06 16:09:23 [INFO]:   - 内容可见: 是
2025-07-06 16:09:23 [INFO]: 测试2: 积分解锁内容
2025-07-06 16:09:23 [INFO]: ✅ 创建积分解锁内容成功，ID: 5，价格: 100 积分
2025-07-06 16:09:23 [INFO]: ✅ 未登录用户解锁状态: 未解锁
2025-07-06 16:09:23 [INFO]: ✅ 积分内容详情获取成功: 积分解锁内容测试 - 1751789363202
2025-07-06 16:09:23 [INFO]:   - 解锁状态: 未解锁
2025-07-06 16:09:23 [INFO]:   - 内容可见: 完整内容
2025-07-06 16:09:23 [INFO]:   - 解锁价格: 100 积分
2025-07-06 16:09:23 [INFO]: 测试3: VIP解锁内容
2025-07-06 16:09:23 [INFO]: ✅ 创建VIP解锁内容成功，ID: 6
2025-07-06 16:09:23 [INFO]: ✅ VIP内容详情获取成功: VIP解锁内容测试 - 1751789363240
2025-07-06 16:09:23 [INFO]:   - 解锁方式: VIP专享
2025-07-06 16:09:23 [INFO]:   - 解锁状态: 需要VIP
2025-07-06 16:09:23 [INFO]: 测试4: 卡密解锁内容
2025-07-06 16:09:23 [INFO]: ✅ 创建卡密解锁内容成功，ID: 7
2025-07-06 16:09:23 [INFO]: ✅ 卡密内容详情获取成功: 卡密解锁内容测试 - 1751789363270
2025-07-06 16:09:23 [INFO]:   - 解锁方式: 卡密解锁
2025-07-06 16:09:23 [INFO]:   - 解锁状态: 需要卡密
2025-07-06 16:09:23 [INFO]: 测试5: 内容搜索和筛选功能
2025-07-06 16:09:23 [INFO]: ✅ 按分类筛选成功，找到 1 个内容
2025-07-06 16:09:23 [INFO]: ✅ 按内容类型筛选成功，找到 2 个文章
2025-07-06 16:09:23 [INFO]: ✅ 按解锁类型筛选成功，找到 2 个免费内容
2025-07-06 16:09:23 [INFO]: ✅ 关键词搜索成功，找到 2 个相关内容
2025-07-06 16:14:22 [INFO]: 🚀 测试服务器启动成功，端口: 3000
2025-07-06 16:14:22 [INFO]: 📱 微信扫码登录演示页面: http://localhost:3000/wechat-qr-login.html
2025-07-06 16:15:26 [INFO]: 开始测试管理后台系统功能...
2025-07-06 16:15:26 [INFO]: 测试1: 管理员登录
2025-07-06 16:15:26 [INFO]: ✅ 错误密码登录被正确拒绝
2025-07-06 16:15:26 [INFO]: ✅ 管理员登录成功
2025-07-06 16:15:26 [INFO]:   - 管理员: 系统管理员 (admin)
2025-07-06 16:15:26 [INFO]:   - 角色: 超级管理员
2025-07-06 16:15:26 [INFO]:   - 令牌: demo_admin_token_175...
2025-07-06 16:15:26 [INFO]:   - 有效期: 604800 秒
2025-07-06 16:15:26 [INFO]: 测试2: 获取管理员信息
2025-07-06 16:15:26 [INFO]: ✅ 获取管理员信息成功
2025-07-06 16:15:26 [INFO]:   - 用户名: admin
2025-07-06 16:15:26 [INFO]:   - 昵称: 系统管理员
2025-07-06 16:15:26 [INFO]:   - 邮箱: <EMAIL>
2025-07-06 16:15:26 [INFO]:   - 权限: *
2025-07-06 16:15:26 [INFO]: 测试3: 获取统计数据
2025-07-06 16:15:26 [INFO]: ✅ 获取统计数据成功
2025-07-06 16:15:26 [INFO]:   - 总用户数: 1234
2025-07-06 16:15:26 [INFO]:   - 活跃用户: 856
2025-07-06 16:15:26 [INFO]:   - 本周新增: 45
2025-07-06 16:15:26 [INFO]:   - 总内容数: 2
2025-07-06 16:15:26 [INFO]:   - 今日浏览: 2345
2025-07-06 16:15:26 [INFO]:   - 总收入: ¥12345
2025-07-06 16:15:26 [INFO]:   - 本月收入: ¥3456
2025-07-06 16:15:26 [INFO]: 测试4: 管理员内容管理权限
2025-07-06 16:15:26 [INFO]: ✅ 管理员创建内容成功，ID: 3
2025-07-06 16:15:26 [INFO]: ✅ 管理员更新内容成功
2025-07-06 16:15:26 [INFO]: ✅ 管理员删除内容成功
2025-07-06 16:15:26 [INFO]: 测试5: 管理员分类管理权限
2025-07-06 16:15:26 [INFO]: ✅ 管理员创建分类成功，ID: 4
2025-07-06 16:15:26 [INFO]: ✅ 管理员删除分类成功
2025-07-06 16:15:26 [INFO]: 测试6: 权限验证
2025-07-06 16:15:26 [INFO]: ✅ 无效令牌被正确拒绝
2025-07-06 16:15:26 [INFO]: ✅ 无令牌访问被正确拒绝
2025-07-06 16:19:51 [INFO]: 🚀 测试服务器启动成功，端口: 3000
2025-07-06 16:19:51 [INFO]: 📱 微信扫码登录演示页面: http://localhost:3000/wechat-qr-login.html
2025-07-06 16:20:52 [INFO]: 开始测试积分配置和卡密管理功能...
2025-07-06 16:20:52 [INFO]: 步骤1: 管理员登录
2025-07-06 16:20:52 [INFO]: ✅ 管理员登录成功，令牌: demo_admin_token_175...
2025-07-06 16:20:52 [INFO]: 步骤2: 测试积分配置功能
2025-07-06 16:20:52 [INFO]: ✅ 获取积分配置成功，共 5 个配置
2025-07-06 16:20:52 [INFO]:   - daily_sign: 10 积分 (每日签到奖励)
2025-07-06 16:20:52 [INFO]:   - watch_ad: 5 积分 (观看广告奖励)
2025-07-06 16:20:52 [INFO]:   - invite_user: 50 积分 (邀请用户奖励)
2025-07-06 16:20:52 [INFO]:   - first_share: 20 积分 (首次分享奖励)
2025-07-06 16:20:52 [INFO]:   - complete_profile: 30 积分 (完善资料奖励)
2025-07-06 16:20:52 [INFO]: ✅ 更新积分配置成功
2025-07-06 16:20:52 [INFO]:   - 每日签到奖励更新为: 15 积分
2025-07-06 16:20:52 [INFO]: ✅ 批量更新积分配置成功
2025-07-06 16:20:52 [INFO]: 步骤3: 测试卡密管理功能
2025-07-06 16:20:52 [INFO]: ✅ 获取卡密统计成功
2025-07-06 16:20:52 [INFO]:   - 总卡密数: 2
2025-07-06 16:20:52 [INFO]:   - 未使用: 1
2025-07-06 16:20:52 [INFO]:   - 已使用: 1
2025-07-06 16:20:52 [INFO]:   - 已过期: 0
2025-07-06 16:20:52 [INFO]:   - 积分卡: 1
2025-07-06 16:20:52 [INFO]:   - 内容解锁卡: 1
2025-07-06 16:20:52 [INFO]: ✅ 批量生成卡密成功，生成 5 个卡密
2025-07-06 16:20:52 [INFO]:   - 4PXHTIVAS71EWUHA (价值: 50 积分)
2025-07-06 16:20:52 [INFO]:   - 4CGZJXLYHG4OKKT0 (价值: 50 积分)
2025-07-06 16:20:52 [INFO]:   - XT4RBA8ZSA662I8P (价值: 50 积分)
2025-07-06 16:20:53 [INFO]: ✅ 获取卡密列表成功，共 7 个卡密
2025-07-06 16:20:53 [INFO]: ✅ 删除卡密成功: ABCD1234EFGH5678
2025-07-06 16:20:53 [INFO]: 步骤4: 测试权限验证
2025-07-06 16:20:53 [INFO]: ✅ 无效令牌被正确拒绝
2025-07-06 16:20:53 [INFO]: ✅ 无令牌访问被正确拒绝
2025-07-06 16:22:09 [INFO]: 🚀 开始知识付费平台完整系统测试...
2025-07-06 16:22:09 [INFO]: 
2025-07-06 16:22:09 [INFO]: 📱 测试1: 微信扫码登录功能
2025-07-06 16:22:09 [INFO]: 生成微信扫码登录二维码
2025-07-06 16:22:09 [INFO]: ✅ 生成微信登录二维码成功，票据: qr_1751790129587_r70jlsaua
2025-07-06 16:22:09 [INFO]: ✅ 登录状态检查正常: waiting
2025-07-06 16:22:09 [INFO]: ✅ 模拟扫码登录成功
2025-07-06 16:22:09 [INFO]: 📚 测试2: 内容管理系统
2025-07-06 16:22:09 [INFO]: ✅ 获取分类列表成功，共 3 个分类
2025-07-06 16:22:09 [INFO]: ✅ 获取内容列表成功，共 2 个内容
2025-07-06 16:22:09 [INFO]: ✅ 内容搜索功能正常，找到 2 个结果
2025-07-06 16:22:09 [INFO]: ✅ 获取内容详情成功: 示例文章1
2025-07-06 16:22:09 [INFO]: ✅ 解锁状态检查正常: 已解锁
2025-07-06 16:22:09 [INFO]: 🔧 测试3: 管理后台系统
2025-07-06 16:22:09 [INFO]: ✅ 管理员登录成功
2025-07-06 16:22:09 [INFO]: ✅ 获取管理员信息成功: 系统管理员
2025-07-06 16:22:09 [INFO]: ✅ 获取统计数据成功 - 用户: 1234, 内容: 2
2025-07-06 16:22:09 [INFO]: ✅ 管理员创建内容成功，ID: 3
2025-07-06 16:22:09 [INFO]: ✅ 管理员删除内容成功
2025-07-06 16:22:09 [INFO]: 💰 测试4: 积分配置和卡密管理
2025-07-06 16:22:09 [INFO]: ✅ 获取积分配置成功，共 5 个配置
2025-07-06 16:22:09 [INFO]: ✅ 获取卡密统计成功 - 总数: 6, 未使用: 5
2025-07-06 16:22:09 [INFO]: ✅ 生成测试卡密成功，数量: 3
2025-07-06 16:22:09 [INFO]: 🔒 测试5: 权限验证
2025-07-06 16:22:09 [INFO]: ✅ 无效令牌被正确拒绝
2025-07-06 16:22:09 [INFO]: ✅ 无令牌访问被正确拒绝
2025-07-06 16:32:35 [INFO]: 🔍 开始知识付费平台全面功能检测...
2025-07-06 16:32:35 [INFO]: 
2025-07-06 16:32:35 [INFO]: 🎨 开始前端功能检测...
2025-07-06 16:32:35 [INFO]: 📄 检测页面可访问性...
2025-07-06 16:32:35 [INFO]: ✅ 微信扫码登录 页面可访问 (200)
2025-07-06 16:32:35 [INFO]: ✅ 内容管理演示 页面可访问 (200)
2025-07-06 16:32:35 [INFO]: ✅ 管理员登录 页面可访问 (200)
2025-07-06 16:32:35 [INFO]: ✅ 管理后台 页面可访问 (200)
2025-07-06 16:32:35 [INFO]: ✅ 积分卡密管理 页面可访问 (200)
2025-07-06 16:32:35 [INFO]: 📱 检测微信扫码登录页面功能...
2025-07-06 16:32:35 [INFO]: 生成微信扫码登录二维码
2025-07-06 16:32:35 [INFO]: ✅ 微信扫码登录页面功能正常
2025-07-06 16:32:35 [INFO]: 📚 检测内容管理页面功能...
2025-07-06 16:32:35 [INFO]: ✅ 内容管理页面功能正常
2025-07-06 16:32:35 [INFO]: 🔧 检测管理后台页面功能...
2025-07-06 16:32:35 [INFO]: ✅ 管理后台页面功能正常
2025-07-06 16:32:35 [INFO]: 💰 检测积分卡密管理页面功能...
2025-07-06 16:32:35 [INFO]: ✅ 积分卡密管理页面功能正常
2025-07-06 16:32:35 [INFO]: 🔌 开始后端API功能检测...
2025-07-06 16:32:35 [INFO]: 🌐 检测REST API端点...
2025-07-06 16:32:35 [INFO]: ✅ GET /categories - 获取分类列表
2025-07-06 16:32:35 [INFO]: ✅ GET /content - 获取内容列表
2025-07-06 16:32:35 [INFO]: ✅ GET /content/search?keyword=test - 搜索内容
2025-07-06 16:32:36 [INFO]: ✅ GET /content/1 - 获取内容详情
2025-07-06 16:32:36 [INFO]: ✅ GET /content/1/unlock-status - 检查解锁状态
2025-07-06 16:32:36 [INFO]: 生成微信扫码登录二维码
2025-07-06 16:32:36 [INFO]: ✅ POST /wechat/qr-login - 生成微信登录二维码
2025-07-06 16:32:36 [INFO]: ✅ POST /admin/login - 管理员登录
2025-07-06 16:32:36 [INFO]: 🔐 检测用户认证API...
2025-07-06 16:32:36 [INFO]: ✅ 用户认证API功能正常
2025-07-06 16:32:36 [INFO]: 📝 检测CRUD操作...
2025-07-06 16:32:36 [INFO]: ✅ CRUD操作功能正常
2025-07-06 16:32:36 [INFO]: 🛡️ 检测权限控制...
2025-07-06 16:32:36 [INFO]: ✅ 权限控制检测完成
2025-07-06 16:32:36 [INFO]: 🔒 检测安全机制...
2025-07-06 16:32:36 [INFO]: ✅ 安全机制检测完成
2025-07-06 16:32:36 [INFO]: 🔗 开始系统集成检测...
2025-07-06 16:32:36 [INFO]: 📊 检测前后端数据流...
2025-07-06 16:32:36 [INFO]: ✅ 数据流检测完成
2025-07-06 16:32:36 [INFO]: 🔐 检测权限一致性...
2025-07-06 16:32:36 [INFO]: ✅ 权限一致性检测完成
2025-07-06 16:32:36 [INFO]: ⚡ 检测系统性能...
2025-07-06 16:32:36 [INFO]: ✅ 性能检测完成
2025-07-06 16:32:36 [INFO]: 🔄 检测端到端业务流程...
2025-07-06 16:32:36 [INFO]: 生成微信扫码登录二维码
2025-07-06 16:32:36 [INFO]: ✅ 端到端业务流程检测完成
2025-07-06 16:33:56 [INFO]: 生成微信扫码登录二维码
2025-07-06 16:35:12 [INFO]: 🎨 开始前端功能验证测试...
2025-07-06 16:35:12 [INFO]: 📄 验证HTML结构...
2025-07-06 16:35:15 [ERROR]: ❌ 微信扫码登录 HTML验证失败: Request failed with status code 502
2025-07-06 16:35:18 [ERROR]: ❌ 内容管理演示 HTML验证失败: Request failed with status code 502
2025-07-06 16:35:21 [ERROR]: ❌ 管理员登录 HTML验证失败: Request failed with status code 502
2025-07-06 16:35:24 [ERROR]: ❌ 管理后台 HTML验证失败: Request failed with status code 502
2025-07-06 16:35:26 [ERROR]: ❌ 积分卡密管理 HTML验证失败: Request failed with status code 502
2025-07-06 16:35:26 [INFO]: 🎨 验证CSS响应式设计...
2025-07-06 16:35:28 [ERROR]: ❌ 微信扫码登录 CSS验证失败: Request failed with status code 502
2025-07-06 16:35:30 [ERROR]: ❌ 管理员登录 CSS验证失败: Request failed with status code 502
2025-07-06 16:35:34 [ERROR]: ❌ 管理后台 CSS验证失败: Request failed with status code 502
2025-07-06 16:35:34 [INFO]: ⚡ 验证JavaScript功能...
2025-07-06 16:35:37 [ERROR]: ❌ 微信扫码登录 JavaScript验证失败: Request failed with status code 502
2025-07-06 16:35:43 [ERROR]: ❌ 内容管理演示 JavaScript验证失败: Request failed with status code 502
2025-07-06 16:35:45 [ERROR]: ❌ 管理员登录 JavaScript验证失败: Request failed with status code 502
2025-07-06 16:35:48 [ERROR]: ❌ 管理后台 JavaScript验证失败: Request failed with status code 502
2025-07-06 16:35:51 [ERROR]: ❌ 积分卡密管理 JavaScript验证失败: Request failed with status code 502
2025-07-06 16:35:51 [INFO]: ♿ 验证可访问性...
2025-07-06 16:35:54 [ERROR]: ❌ 微信扫码登录 可访问性验证失败: Request failed with status code 502
2025-07-06 16:35:57 [ERROR]: ❌ 管理员登录 可访问性验证失败: Request failed with status code 502
2025-07-06 16:35:57 [INFO]: ⚡ 验证性能优化...
2025-07-06 16:36:00 [ERROR]: ❌ 微信扫码登录 性能验证失败: Request failed with status code 502
2025-07-06 16:36:03 [ERROR]: ❌ 管理后台 性能验证失败: Request failed with status code 502
2025-07-06 16:38:25 [INFO]: 🎨 开始前端功能验证测试...
2025-07-06 16:38:25 [INFO]: 📄 验证HTML结构...
2025-07-06 16:38:25 [INFO]: ✅ 微信扫码登录 HTML结构验证通过 (8/8)
2025-07-06 16:38:25 [INFO]: ✅ 内容管理演示 HTML结构验证通过 (8/8)
2025-07-06 16:38:25 [INFO]: ✅ 管理员登录 HTML结构验证通过 (8/8)
2025-07-06 16:38:25 [INFO]: ✅ 管理后台 HTML结构验证通过 (8/8)
2025-07-06 16:38:25 [INFO]: ✅ 积分卡密管理 HTML结构验证通过 (8/8)
2025-07-06 16:38:25 [INFO]: 🎨 验证CSS响应式设计...
2025-07-06 16:38:25 [WARN]: ⚠️ 微信扫码登录 CSS响应式设计需要改进 (2/7)
2025-07-06 16:38:26 [WARN]: ⚠️ 管理员登录 CSS响应式设计需要改进 (3/7)
2025-07-06 16:38:26 [INFO]: ✅ 管理后台 CSS响应式设计良好 (5/7)
2025-07-06 16:38:26 [INFO]: ⚡ 验证JavaScript功能...
2025-07-06 16:38:26 [INFO]: ✅ 微信扫码登录 JavaScript功能完善 (5/7)
2025-07-06 16:38:26 [INFO]: ✅ 内容管理演示 JavaScript功能完善 (6/7)
2025-07-06 16:38:26 [INFO]: ✅ 管理员登录 JavaScript功能完善 (7/7)
2025-07-06 16:38:26 [INFO]: ✅ 管理后台 JavaScript功能完善 (6/7)
2025-07-06 16:38:26 [INFO]: ✅ 积分卡密管理 JavaScript功能完善 (7/7)
2025-07-06 16:38:26 [INFO]: ♿ 验证可访问性...
2025-07-06 16:38:26 [WARN]: ⚠️ 微信扫码登录 可访问性需要改进 (1/6)
2025-07-06 16:38:26 [WARN]: ⚠️ 管理员登录 可访问性需要改进 (2/6)
2025-07-06 16:38:26 [INFO]: ⚡ 验证性能优化...
2025-07-06 16:38:26 [INFO]: ✅ 微信扫码登录 性能良好 (8ms, 12.12KB)
2025-07-06 16:38:26 [INFO]: ✅ 管理后台 性能良好 (11ms, 25.62KB)
2025-07-06 16:40:20 [INFO]: 🎬 知识付费平台最终功能演示开始...
2025-07-06 16:40:20 [INFO]: 
2025-07-06 16:40:20 [INFO]: 📱 演示1: 微信扫码登录完整流程
2025-07-06 16:40:20 [INFO]: ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
2025-07-06 16:40:20 [INFO]: 生成微信扫码登录二维码
2025-07-06 16:40:20 [INFO]: ✅ 步骤1: 生成微信登录二维码成功
2025-07-06 16:40:20 [INFO]:    票据: qr_1751791220774_5ozfy7rjc
2025-07-06 16:40:20 [INFO]:    二维码URL: undefined
2025-07-06 16:40:20 [INFO]: ✅ 步骤2: 检查登录状态 - waiting
2025-07-06 16:40:20 [INFO]: ✅ 步骤3: 模拟用户扫码成功
2025-07-06 16:40:20 [INFO]: ✅ 步骤4: 用户确认登录成功
2025-07-06 16:40:20 [INFO]:    用户信息: {"id":750,"openid":"mock_openid_1751791220819","nickname":"微信用户573","avatar":"https://via.placeholder.com/100x100"}
2025-07-06 16:40:20 [INFO]:    访问令牌: mock_token_175179122...
2025-07-06 16:40:20 [INFO]: 
2025-07-06 16:40:20 [INFO]: 🔧 演示2: 管理员登录和后台管理
2025-07-06 16:40:20 [INFO]: ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
2025-07-06 16:40:20 [INFO]: ✅ 步骤1: 管理员登录成功
2025-07-06 16:40:20 [INFO]:    管理员: 系统管理员 (admin)
2025-07-06 16:40:20 [INFO]:    角色: 超级管理员
2025-07-06 16:40:20 [INFO]: ✅ 步骤2: 获取系统统计数据
2025-07-06 16:40:20 [INFO]:    总用户数: 1234
2025-07-06 16:40:20 [INFO]:    总内容数: 2
2025-07-06 16:40:20 [INFO]:    总收入: ¥12345
2025-07-06 16:40:20 [INFO]: ✅ 步骤3: 创建内容成功
2025-07-06 16:40:20 [INFO]:    内容ID: 6
2025-07-06 16:40:20 [INFO]:    标题: 演示内容 - 7/6/2025, 4:40:20 PM
2025-07-06 16:40:20 [INFO]:    解锁价格: 50 积分
2025-07-06 16:40:20 [INFO]: ✅ 步骤4: 更新内容成功
2025-07-06 16:40:20 [INFO]: ✅ 步骤5: 删除测试内容成功
2025-07-06 16:40:20 [INFO]: 
2025-07-06 16:40:20 [INFO]: 📚 演示3: 内容浏览和解锁机制
2025-07-06 16:40:20 [INFO]: ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
2025-07-06 16:40:20 [INFO]: ✅ 步骤1: 获取分类列表成功，共 3 个分类
2025-07-06 16:40:20 [INFO]:    - 技术文章 (tech)
2025-07-06 16:40:20 [INFO]:    - 生活分享 (life)
2025-07-06 16:40:20 [INFO]:    - 学习资源 (study)
2025-07-06 16:40:20 [INFO]: ✅ 步骤2: 获取内容列表成功，共 2 个内容
2025-07-06 16:40:20 [INFO]:    示例内容: 示例文章1
2025-07-06 16:40:20 [INFO]:    解锁方式: 免费
2025-07-06 16:40:20 [INFO]:    浏览次数: 103
2025-07-06 16:40:20 [INFO]: ✅ 步骤3: 获取内容详情成功
2025-07-06 16:40:20 [INFO]:    解锁状态: 已解锁
2025-07-06 16:40:20 [INFO]: ✅ 步骤4: 检查解锁状态成功
2025-07-06 16:40:20 [INFO]: ✅ 步骤5: 内容搜索成功，找到 2 个结果
2025-07-06 16:40:20 [INFO]: 
2025-07-06 16:40:20 [INFO]: 💰 演示4: 积分配置和卡密管理
2025-07-06 16:40:20 [INFO]: ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
2025-07-06 16:40:20 [INFO]: ✅ 步骤1: 获取积分配置成功，共 5 个配置
2025-07-06 16:40:20 [INFO]:    - 每日签到奖励（已更新）: 15 积分
2025-07-06 16:40:20 [INFO]:    - 观看广告奖励（批量更新）: 8 积分
2025-07-06 16:40:20 [INFO]:    - 邀请用户奖励（批量更新）: 60 积分
2025-07-06 16:40:20 [INFO]:    - 首次分享奖励: 20 积分
2025-07-06 16:40:20 [INFO]:    - 完善资料奖励: 30 积分
2025-07-06 16:40:20 [INFO]: ✅ 步骤2: 获取卡密统计成功
2025-07-06 16:40:20 [INFO]:    总卡密数: 9
2025-07-06 16:40:20 [INFO]:    未使用: 8
2025-07-06 16:40:20 [INFO]:    已使用: 1
2025-07-06 16:40:20 [INFO]: ✅ 步骤3: 生成演示卡密成功，数量: 3
2025-07-06 16:50:52 [INFO]: 🔍 开始验证部署状态...
2025-07-06 16:50:52 [INFO]: 
2025-07-06 16:50:52 [INFO]: 📡 验证服务器启动状态...
2025-07-06 16:50:52 [ERROR]: ❌ 服务器未启动或无法访问
2025-07-06 16:50:52 [ERROR]:    错误: Request failed with status code 404
2025-07-06 16:50:52 [INFO]: 🗄️ 验证数据库连接...
2025-07-06 16:50:52 [INFO]: ✅ 数据库连接正常
2025-07-06 16:50:52 [INFO]:    获取到 3 个分类
2025-07-06 16:50:52 [INFO]: 🎨 验证前端页面...
2025-07-06 16:50:52 [INFO]: ✅ 微信扫码登录 页面正常
2025-07-06 16:50:53 [INFO]: ✅ 内容管理演示 页面正常
2025-07-06 16:50:53 [INFO]: ✅ 管理员登录 页面正常
2025-07-06 16:50:53 [INFO]: ✅ 管理后台 页面正常
2025-07-06 16:50:53 [INFO]: ✅ 积分卡密管理 页面正常
2025-07-06 16:50:53 [INFO]: ✅ 前端页面验证通过 (5/5)
2025-07-06 16:50:53 [INFO]: 🔌 验证后端API...
2025-07-06 16:50:53 [INFO]: ✅ 获取分类列表 API正常
2025-07-06 16:50:53 [INFO]: ✅ 获取内容列表 API正常
2025-07-06 16:50:53 [INFO]: ✅ 内容搜索 API正常
2025-07-06 16:50:53 [INFO]: 生成微信扫码登录二维码
2025-07-06 16:50:53 [INFO]: ✅ 生成微信二维码 API正常
2025-07-06 16:50:53 [INFO]: ✅ 后端API验证通过 (4/4)
2025-07-06 16:50:53 [INFO]: 👨‍💼 验证管理员功能...
2025-07-06 16:50:53 [INFO]: ✅ 管理员登录成功
2025-07-06 16:50:53 [INFO]: ✅ 管理员权限验证通过
2025-07-06 16:51:19 [INFO]: 🔍 开始验证部署状态...
2025-07-06 16:51:19 [INFO]: 
2025-07-06 16:51:19 [INFO]: 📡 验证服务器启动状态...
2025-07-06 16:51:19 [INFO]: ✅ 服务器启动正常
2025-07-06 16:51:19 [INFO]: 🗄️ 验证数据库连接...
2025-07-06 16:51:19 [INFO]: ✅ 数据库连接正常
2025-07-06 16:51:19 [INFO]:    获取到 3 个分类
2025-07-06 16:51:19 [INFO]: 🎨 验证前端页面...
2025-07-06 16:51:19 [INFO]: ✅ 微信扫码登录 页面正常
2025-07-06 16:51:19 [INFO]: ✅ 内容管理演示 页面正常
2025-07-06 16:51:19 [INFO]: ✅ 管理员登录 页面正常
2025-07-06 16:51:19 [INFO]: ✅ 管理后台 页面正常
2025-07-06 16:51:19 [INFO]: ✅ 积分卡密管理 页面正常
2025-07-06 16:51:19 [INFO]: ✅ 前端页面验证通过 (5/5)
2025-07-06 16:51:19 [INFO]: 🔌 验证后端API...
2025-07-06 16:51:19 [INFO]: ✅ 获取分类列表 API正常
2025-07-06 16:51:19 [INFO]: ✅ 获取内容列表 API正常
2025-07-06 16:51:19 [INFO]: ✅ 内容搜索 API正常
2025-07-06 16:51:19 [INFO]: 生成微信扫码登录二维码
2025-07-06 16:51:19 [INFO]: ✅ 生成微信二维码 API正常
2025-07-06 16:51:19 [INFO]: ✅ 后端API验证通过 (4/4)
2025-07-06 16:51:19 [INFO]: 👨‍💼 验证管理员功能...
2025-07-06 16:51:19 [INFO]: ✅ 管理员登录成功
2025-07-06 16:51:19 [INFO]: ✅ 管理员权限验证通过
2025-07-06 17:22:19 [INFO]: 🚀 启动微信知识付费平台后端服务...
2025-07-06 17:22:19 [INFO]: 开始初始化数据库...
2025-07-06 17:22:20 [INFO]: 数据库连接成功
2025-07-06 17:22:20 [ERROR]: 数据库同步失败
Error
    at Query.run (/root/zhis/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
    at /root/zhis/backend/node_modules/sequelize/lib/sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.addIndex (/root/zhis/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:250:12)
    at async Tag.sync (/root/zhis/backend/node_modules/sequelize/lib/model.js:999:7)
    at async Sequelize.sync (/root/zhis/backend/node_modules/sequelize/lib/sequelize.js:377:9)
    at async syncDatabase (/root/zhis/backend/src/models/index.js:176:5)
    at async initDatabase (/root/zhis/backend/database/init.js:14:5)
    at async start (/root/zhis/backend/scripts/start.js:9:5)
