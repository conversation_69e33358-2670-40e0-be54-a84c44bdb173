import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ 
  showSpinner: false,
  minimum: 0.2,
  speed: 500
})

// 路由配置
const routes = [
  // 首页
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: {
      title: '首页',
      keepAlive: true
    }
  },

  // 分类页面
  {
    path: '/category',
    name: 'Category',
    component: () => import('@/views/Category.vue'),
    meta: {
      title: '分类浏览',
      keepAlive: true
    }
  },

  // 搜索页面
  {
    path: '/search',
    name: 'Search',
    component: () => import('@/views/Search.vue'),
    meta: {
      title: '搜索内容',
      keepAlive: true
    }
  },

  // 内容详情页
  {
    path: '/content/:id',
    name: 'ContentDetail',
    component: () => import('@/views/ContentDetail.vue'),
    meta: {
      title: '内容详情'
    }
  },

  // 用户认证相关
  {
    path: '/auth',
    component: () => import('@/views/auth/Layout.vue'),
    meta: {
      hideForAuth: true
    },
    children: [
      {
        path: 'login',
        name: 'Login',
        component: () => import('@/views/auth/Login.vue'),
        meta: {
          title: '用户登录',
          hideForAuth: true
        }
      },
      {
        path: 'register',
        name: 'Register',
        component: () => import('@/views/auth/Register.vue'),
        meta: {
          title: '用户注册',
          hideForAuth: true
        }
      }
    ]
  },

  // 用户中心相关
  {
    path: '/user',
    component: () => import('@/views/user/Layout.vue'),
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'UserProfile',
        component: () => import('@/views/user/Profile.vue'),
        meta: {
          title: '个人中心',
          requiresAuth: true
        }
      },
      {
        path: 'points',
        name: 'UserPoints',
        component: () => import('@/views/user/Points.vue'),
        meta: {
          title: '积分中心',
          requiresAuth: true
        }
      },
      {
        path: 'vip',
        name: 'UserVip',
        component: () => import('@/views/user/Vip.vue'),
        meta: {
          title: 'VIP中心',
          requiresAuth: true
        }
      },
      {
        path: 'favorites',
        name: 'UserFavorites',
        component: () => import('@/views/user/Favorites.vue'),
        meta: {
          title: '我的收藏',
          requiresAuth: true
        }
      },
      {
        path: 'unlocks',
        name: 'UserUnlocks',
        component: () => import('@/views/user/Unlocks.vue'),
        meta: {
          title: '解锁记录',
          requiresAuth: true
        }
      },
      {
        path: 'invite',
        name: 'UserInvite',
        component: () => import('@/views/user/Invite.vue'),
        meta: {
          title: '邀请好友',
          requiresAuth: true
        }
      }
    ]
  },

  // 积分相关功能页面
  {
    path: '/points',
    children: [
      {
        path: 'earn',
        name: 'PointsEarn',
        component: () => import('@/views/points/Earn.vue'),
        meta: {
          title: '积分获取',
          requiresAuth: true
        }
      },
      {
        path: 'sign',
        name: 'PointsSign',
        component: () => import('@/views/points/Sign.vue'),
        meta: {
          title: '每日签到',
          requiresAuth: true
        }
      }
    ]
  },

  // 卡密兑换
  {
    path: '/card-exchange',
    name: 'CardExchange',
    component: () => import('@/views/CardExchange.vue'),
    meta: {
      title: '卡密兑换',
      requiresAuth: true
    }
  },

  // 静态页面
  {
    path: '/about',
    name: 'About',
    component: () => import('@/views/About.vue'),
    meta: {
      title: '关于我们'
    }
  },
  {
    path: '/privacy',
    name: 'Privacy',
    component: () => import('@/views/Privacy.vue'),
    meta: {
      title: '隐私政策'
    }
  },
  {
    path: '/terms',
    name: 'Terms',
    component: () => import('@/views/Terms.vue'),
    meta: {
      title: '服务条款'
    }
  },

  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面不存在'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start()
  
  // 设置页面标题
  const title = to.meta.title
  if (title) {
    document.title = `${title} - 知识付费平台`
  }
  
  const userStore = useUserStore()
  
  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    if (!userStore.isLoggedIn) {
      // 保存目标路由，登录后跳转
      sessionStorage.setItem('redirectPath', to.fullPath)
      next('/auth/login')
      return
    }
  }
  
  // 已登录用户访问登录/注册页面，重定向到首页
  if (to.meta.hideForAuth && userStore.isLoggedIn) {
    next('/')
    return
  }
  
  next()
})

// 全局后置守卫
router.afterEach((to, from) => {
  // 结束进度条
  NProgress.done()
  
  // 页面访问统计
  if (typeof gtag !== 'undefined') {
    gtag('config', 'GA_MEASUREMENT_ID', {
      page_path: to.path
    })
  }
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  NProgress.done()
})

export default router
