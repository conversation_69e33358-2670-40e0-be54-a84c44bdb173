<template>
  <div class="vip-page">
    <!-- VIP状态卡片 -->
    <el-card class="vip-status-card">
      <div class="vip-status">
        <div class="status-icon">
          <el-icon class="crown-icon"><Crown /></el-icon>
        </div>
        
        <div class="status-info">
          <h2 v-if="userStore.isVip">VIP会员</h2>
          <h2 v-else>普通用户</h2>
          
          <div v-if="userStore.isVip" class="vip-info">
            <p>VIP到期时间：{{ formatTime(vipInfo.expireTime) }}</p>
            <p>剩余天数：{{ vipInfo.remainingDays }} 天</p>
          </div>
          <div v-else class="normal-info">
            <p>开通VIP享受更多特权</p>
          </div>
        </div>
        
        <div class="status-action">
          <el-button 
            v-if="!userStore.isVip" 
            type="primary" 
            size="large"
            @click="showVipPlans = true"
          >
            立即开通
          </el-button>
          <el-button 
            v-else 
            type="warning" 
            size="large"
            @click="showVipPlans = true"
          >
            续费VIP
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- VIP特权 -->
    <el-card class="vip-privileges-card">
      <template #header>
        <h3>VIP特权</h3>
      </template>

      <div class="privileges-grid">
        <div 
          v-for="privilege in vipPrivileges" 
          :key="privilege.id"
          :class="['privilege-item', { 'available': userStore.isVip }]"
        >
          <div class="privilege-icon">
            <el-icon>
              <component :is="privilege.icon" />
            </el-icon>
          </div>
          <div class="privilege-info">
            <h4>{{ privilege.title }}</h4>
            <p>{{ privilege.description }}</p>
          </div>
          <div class="privilege-status">
            <el-tag 
              v-if="userStore.isVip" 
              type="success" 
              size="small"
            >
              已享受
            </el-tag>
            <el-tag 
              v-else 
              type="info" 
              size="small"
            >
              开通后享受
            </el-tag>
          </div>
        </div>
      </div>
    </el-card>

    <!-- VIP专享内容 -->
    <el-card class="vip-content-card">
      <template #header>
        <div class="card-header">
          <h3>VIP专享内容</h3>
          <el-button text type="primary" @click="$router.push('/category?unlockType=vip')">
            查看更多
          </el-button>
        </div>
      </template>

      <div v-if="vipContentLoading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>

      <div v-else-if="vipContents.length > 0" class="content-list">
        <div 
          v-for="content in vipContents" 
          :key="content.id"
          class="content-item"
          @click="$router.push(`/content/${content.id}`)"
        >
          <div class="content-image">
            <img :src="content.coverImage" :alt="content.title" />
            <div class="vip-badge">VIP</div>
          </div>
          <div class="content-info">
            <h4>{{ content.title }}</h4>
            <p>{{ content.summary }}</p>
            <div class="content-meta">
              <span>{{ content.viewCount }} 浏览</span>
              <span>{{ formatTime(content.createdAt) }}</span>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="empty-content">
        <el-empty description="暂无VIP专享内容" />
      </div>
    </el-card>

    <!-- VIP套餐选择对话框 -->
    <el-dialog
      v-model="showVipPlans"
      title="选择VIP套餐"
      width="600px"
      :before-close="handleClosePlans"
    >
      <div class="vip-plans">
        <div 
          v-for="plan in vipPlans" 
          :key="plan.id"
          :class="['plan-item', { 'selected': selectedPlan?.id === plan.id }]"
          @click="selectPlan(plan)"
        >
          <div class="plan-header">
            <h4>{{ plan.name }}</h4>
            <div class="plan-price">
              <span class="price">¥{{ plan.price }}</span>
              <span class="period">/{{ plan.period }}</span>
            </div>
          </div>
          
          <div class="plan-features">
            <div 
              v-for="feature in plan.features" 
              :key="feature"
              class="feature-item"
            >
              <el-icon class="check-icon"><Check /></el-icon>
              <span>{{ feature }}</span>
            </div>
          </div>
          
          <div v-if="plan.discount" class="plan-discount">
            <el-tag type="danger" size="small">{{ plan.discount }}</el-tag>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="handleClosePlans">取消</el-button>
        <el-button 
          type="primary" 
          :disabled="!selectedPlan"
          :loading="purchasing"
          @click="purchaseVip"
        >
          立即开通
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { vip, content } from '@/api'
import { formatTime } from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Crown,
  Unlock,
  Star,
  Download,
  VideoPlay,
  Gift,
  Check
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 状态
const showVipPlans = ref(false)
const vipContentLoading = ref(false)
const purchasing = ref(false)
const selectedPlan = ref(null)

// VIP信息
const vipInfo = reactive({
  expireTime: '',
  remainingDays: 0
})

// VIP专享内容
const vipContents = ref([])

// VIP特权列表
const vipPrivileges = [
  {
    id: 1,
    icon: 'Unlock',
    title: '免费解锁VIP内容',
    description: '无需积分即可解锁所有VIP专享内容'
  },
  {
    id: 2,
    icon: 'Star',
    title: '专属内容推荐',
    description: '获得个性化的高质量内容推荐'
  },
  {
    id: 3,
    icon: 'Download',
    title: '高速下载',
    description: '享受网盘资源高速下载特权'
  },
  {
    id: 4,
    icon: 'VideoPlay',
    title: '无广告体验',
    description: '观看视频内容时无广告打扰'
  },
  {
    id: 5,
    icon: 'Gift',
    title: '专属客服',
    description: '享受VIP专属客服优先服务'
  },
  {
    id: 6,
    icon: 'Crown',
    title: 'VIP身份标识',
    description: '显示专属VIP身份标识和特殊权限'
  }
]

// VIP套餐
const vipPlans = [
  {
    id: 1,
    name: '月度VIP',
    price: 19.9,
    period: '月',
    duration: 30,
    features: [
      '免费解锁VIP内容',
      '专属内容推荐',
      '高速下载',
      '无广告体验'
    ]
  },
  {
    id: 2,
    name: '季度VIP',
    price: 49.9,
    period: '季',
    duration: 90,
    discount: '立省9.8元',
    features: [
      '免费解锁VIP内容',
      '专属内容推荐',
      '高速下载',
      '无广告体验',
      '专属客服'
    ]
  },
  {
    id: 3,
    name: '年度VIP',
    price: 168,
    period: '年',
    duration: 365,
    discount: '立省70.8元',
    features: [
      '免费解锁VIP内容',
      '专属内容推荐',
      '高速下载',
      '无广告体验',
      '专属客服',
      'VIP身份标识'
    ]
  }
]

// 初始化
onMounted(() => {
  if (userStore.isVip) {
    loadVipInfo()
  }
  loadVipContents()
})

// 加载VIP信息
async function loadVipInfo() {
  try {
    const result = await vip.getInfo()
    if (result.code === 200) {
      Object.assign(vipInfo, result.data)
    }
  } catch (error) {
    console.error('加载VIP信息失败:', error)
  }
}

// 加载VIP专享内容
async function loadVipContents() {
  try {
    vipContentLoading.value = true
    
    const result = await content.getList({
      unlockType: 'vip',
      pageSize: 6
    })
    
    if (result.code === 200) {
      vipContents.value = result.data.list
    }
  } catch (error) {
    console.error('加载VIP内容失败:', error)
  } finally {
    vipContentLoading.value = false
  }
}

// 选择套餐
function selectPlan(plan) {
  selectedPlan.value = plan
}

// 购买VIP
async function purchaseVip() {
  if (!selectedPlan.value) {
    ElMessage.warning('请选择VIP套餐')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要开通${selectedPlan.value.name}吗？费用为¥${selectedPlan.value.price}`,
      '确认开通',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    purchasing.value = true
    
    const result = await vip.purchase({
      planId: selectedPlan.value.id,
      duration: selectedPlan.value.duration
    })
    
    if (result.code === 200) {
      ElMessage.success('VIP开通成功！')
      showVipPlans.value = false
      
      // 更新用户信息
      await userStore.fetchUserInfo()
      
      // 重新加载VIP信息
      if (userStore.isVip) {
        await loadVipInfo()
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('购买VIP失败:', error)
    }
  } finally {
    purchasing.value = false
  }
}

// 关闭套餐选择
function handleClosePlans() {
  showVipPlans.value = false
  selectedPlan.value = null
}
</script>

<style lang="scss" scoped>
.vip-page {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.vip-status-card {
  .vip-status {
    display: flex;
    align-items: center;
    gap: 24px;
    
    .status-icon {
      flex-shrink: 0;
      
      .crown-icon {
        font-size: 64px;
        color: #faad14;
      }
    }
    
    .status-info {
      flex: 1;
      
      h2 {
        margin: 0 0 12px 0;
        font-size: 24px;
        font-weight: 600;
        color: #333;
      }
      
      .vip-info,
      .normal-info {
        p {
          margin: 0 0 4px 0;
          color: #666;
          font-size: 14px;
        }
      }
    }
    
    .status-action {
      flex-shrink: 0;
    }
  }
}

.vip-privileges-card {
  .privileges-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    
    .privilege-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 20px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      transition: all 0.3s;
      
      &.available {
        border-color: #faad14;
        background: #fffbf0;
      }
      
      .privilege-icon {
        flex-shrink: 0;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f0f9ff;
        border-radius: 50%;
        
        .el-icon {
          font-size: 24px;
          color: #1890ff;
        }
      }
      
      .privilege-info {
        flex: 1;
        
        h4 {
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
        
        p {
          margin: 0;
          font-size: 14px;
          color: #666;
          line-height: 1.5;
        }
      }
      
      .privilege-status {
        flex-shrink: 0;
      }
    }
  }
}

.vip-content-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .loading-container {
    padding: 20px;
  }
  
  .content-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    
    .content-item {
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-2px);
      }
      
      .content-image {
        position: relative;
        width: 100%;
        height: 160px;
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 12px;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .vip-badge {
          position: absolute;
          top: 8px;
          right: 8px;
          background: #faad14;
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
        }
      }
      
      .content-info {
        h4 {
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        p {
          margin: 0 0 12px 0;
          font-size: 14px;
          color: #666;
          line-height: 1.5;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .content-meta {
          display: flex;
          gap: 16px;
          font-size: 12px;
          color: #999;
        }
      }
    }
  }
  
  .empty-content {
    text-align: center;
    padding: 40px 20px;
  }
}

.vip-plans {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 16px;
  
  .plan-item {
    position: relative;
    padding: 20px;
    border: 2px solid #e4e7ed;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      border-color: #1890ff;
    }
    
    &.selected {
      border-color: #1890ff;
      background: #f0f9ff;
    }
    
    .plan-header {
      text-align: center;
      margin-bottom: 16px;
      
      h4 {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
      
      .plan-price {
        .price {
          font-size: 24px;
          font-weight: 600;
          color: #f5222d;
        }
        
        .period {
          font-size: 14px;
          color: #666;
        }
      }
    }
    
    .plan-features {
      .feature-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        font-size: 14px;
        color: #666;
        
        .check-icon {
          color: #52c41a;
          font-size: 16px;
        }
      }
    }
    
    .plan-discount {
      position: absolute;
      top: -8px;
      right: 8px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .vip-page {
    gap: 16px;
  }
  
  .vip-status-card {
    .vip-status {
      flex-direction: column;
      text-align: center;
      gap: 16px;
    }
  }
  
  .vip-privileges-card {
    .privileges-grid {
      grid-template-columns: 1fr;
      gap: 16px;
      
      .privilege-item {
        padding: 16px;
      }
    }
  }
  
  .vip-content-card {
    .content-list {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
  
  .vip-plans {
    grid-template-columns: 1fr;
    gap: 12px;
    
    .plan-item {
      padding: 16px;
    }
  }
}
</style>
