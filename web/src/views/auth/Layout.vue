<template>
  <div class="auth-layout">
    <div class="auth-container">
      <!-- 左侧背景图 -->
      <div class="auth-bg">
        <div class="bg-overlay">
          <div class="brand-info">
            <h1 class="brand-title">知识付费平台</h1>
            <p class="brand-desc">优质内容，触手可及</p>
            <div class="features">
              <div class="feature-item">
                <el-icon><Star /></el-icon>
                <span>精选优质内容</span>
              </div>
              <div class="feature-item">
                <el-icon><Gift /></el-icon>
                <span>积分奖励机制</span>
              </div>
              <div class="feature-item">
                <el-icon><UserFilled /></el-icon>
                <span>VIP专享权益</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧表单区域 -->
      <div class="auth-form">
        <div class="form-container">
          <router-view />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Star, Gift, UserFilled } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.auth-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.auth-container {
  width: 100%;
  max-width: 1000px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  min-height: 600px;
}

.auth-bg {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .bg-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .brand-info {
    text-align: center;
    color: white;
    z-index: 1;
    
    .brand-title {
      font-size: 32px;
      font-weight: bold;
      margin-bottom: 16px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .brand-desc {
      font-size: 18px;
      margin-bottom: 40px;
      opacity: 0.9;
    }
    
    .features {
      display: flex;
      flex-direction: column;
      gap: 16px;
      
      .feature-item {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 16px;
        
        .el-icon {
          font-size: 20px;
        }
      }
    }
  }
}

.auth-form {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  
  .form-container {
    width: 100%;
    max-width: 400px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .auth-layout {
    padding: 0;
  }
  
  .auth-container {
    flex-direction: column;
    border-radius: 0;
    min-height: 100vh;
  }
  
  .auth-bg {
    min-height: 200px;
    
    .brand-info {
      .brand-title {
        font-size: 24px;
      }
      
      .brand-desc {
        font-size: 16px;
        margin-bottom: 20px;
      }
      
      .features {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
        gap: 12px;
        
        .feature-item {
          font-size: 14px;
          
          .el-icon {
            font-size: 16px;
          }
        }
      }
    }
  }
  
  .auth-form {
    padding: 20px;
  }
}
</style>
