<template>
  <div class="login-form">
    <!-- 标题 -->
    <div class="form-header">
      <h2>欢迎登录</h2>
      <p>登录后享受更多精彩内容</p>
    </div>

    <!-- 登录方式切换 -->
    <el-tabs v-model="loginType" class="login-tabs">
      <!-- 微信扫码登录 -->
      <el-tab-pane label="微信登录" name="wechat">
        <div class="wechat-login">
          <div class="qr-container">
            <div v-if="qrLoading" class="qr-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <p>正在生成二维码...</p>
            </div>
            <div v-else-if="qrCode" class="qr-code">
              <canvas ref="qrCanvas"></canvas>
              <p class="qr-tip">请使用微信扫描二维码登录</p>
            </div>
            <div v-else class="qr-error">
              <el-icon><Warning /></el-icon>
              <p>二维码生成失败</p>
              <el-button @click="generateQRCode" type="primary" size="small">
                重新生成
              </el-button>
            </div>
          </div>
          
          <!-- 登录状态提示 -->
          <div v-if="loginStatus" class="login-status">
            <div v-if="loginStatus === 'scanned'" class="status-item">
              <el-icon class="success"><SuccessFilled /></el-icon>
              <span>扫描成功，请在手机上确认登录</span>
            </div>
            <div v-else-if="loginStatus === 'confirmed'" class="status-item">
              <el-icon class="success"><SuccessFilled /></el-icon>
              <span>登录成功，正在跳转...</span>
            </div>
            <div v-else-if="loginStatus === 'expired'" class="status-item error">
              <el-icon class="error"><CircleCloseFilled /></el-icon>
              <span>二维码已过期，请重新生成</span>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 手机号登录 -->
      <el-tab-pane label="手机登录" name="phone">
        <el-form 
          ref="phoneFormRef" 
          :model="phoneForm" 
          :rules="phoneRules"
          class="phone-form"
          @submit.prevent="handlePhoneLogin"
        >
          <el-form-item prop="phone">
            <el-input
              v-model="phoneForm.phone"
              placeholder="请输入手机号"
              size="large"
              maxlength="11"
              clearable
            >
              <template #prefix>
                <el-icon><Phone /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="code">
            <div class="code-input">
              <el-input
                v-model="phoneForm.code"
                placeholder="请输入验证码"
                size="large"
                maxlength="6"
                clearable
              >
                <template #prefix>
                  <el-icon><Message /></el-icon>
                </template>
              </el-input>
              <el-button
                :disabled="smsCountdown > 0 || !isValidPhone"
                :loading="smsLoading"
                @click="sendSmsCode"
                class="sms-btn"
                size="large"
              >
                {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
              </el-button>
            </div>
          </el-form-item>

          <el-form-item prop="inviteCode" v-if="showInviteCode">
            <el-input
              v-model="phoneForm.inviteCode"
              placeholder="邀请码（选填）"
              size="large"
              clearable
            >
              <template #prefix>
                <el-icon><Gift /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :loading="phoneLoading"
              @click="handlePhoneLogin"
              class="login-btn"
            >
              登录/注册
            </el-button>
          </el-form-item>
        </el-form>

        <div class="form-footer">
          <el-button 
            text 
            @click="showInviteCode = !showInviteCode"
            class="invite-toggle"
          >
            {{ showInviteCode ? '隐藏' : '有邀请码？' }}
          </el-button>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 协议条款 -->
    <div class="agreement">
      <p>
        登录即表示同意
        <router-link to="/terms" target="_blank">《服务条款》</router-link>
        和
        <router-link to="/privacy" target="_blank">《隐私政策》</router-link>
      </p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { auth, wechat } from '@/api'
import { ElMessage } from 'element-plus'
import QRCode from 'qrcode'
import {
  Loading,
  Warning,
  SuccessFilled,
  CircleCloseFilled,
  Phone,
  Message,
  Gift
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 登录方式
const loginType = ref('wechat')

// 微信登录相关
const qrCode = ref('')
const qrLoading = ref(false)
const qrCanvas = ref(null)
const loginStatus = ref('')
const qrTicket = ref('')
let statusCheckTimer = null

// 手机登录相关
const phoneFormRef = ref(null)
const phoneForm = reactive({
  phone: '',
  code: '',
  inviteCode: ''
})
const phoneLoading = ref(false)
const smsLoading = ref(false)
const smsCountdown = ref(0)
const showInviteCode = ref(false)
let smsTimer = null

// 表单验证规则
const phoneRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' }
  ]
}

// 计算属性
const isValidPhone = computed(() => {
  return /^1[3-9]\d{9}$/.test(phoneForm.phone)
})

// 生成微信登录二维码
async function generateQRCode() {
  try {
    qrLoading.value = true
    loginStatus.value = ''

    // 调用后端API生成二维码
    const result = await wechat.generateQRCode()

    if (result.code === 200) {
      qrTicket.value = result.data.ticket
      qrCode.value = result.data.qrUrl

      // 渲染二维码
      await renderQRCode(result.data.qrUrl)

      // 开始检查登录状态
      startStatusCheck()
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    console.error('生成二维码失败:', error)
    ElMessage.error('生成二维码失败，请重试')
    qrCode.value = ''
  } finally {
    qrLoading.value = false
  }
}

// 渲染二维码
async function renderQRCode(url) {
  if (qrCanvas.value) {
    try {
      await QRCode.toCanvas(qrCanvas.value, url, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      })
    } catch (error) {
      console.error('渲染二维码失败:', error)
    }
  }
}

// 开始检查登录状态
function startStatusCheck() {
  if (statusCheckTimer) {
    clearInterval(statusCheckTimer)
  }

  statusCheckTimer = setInterval(async () => {
    try {
      const result = await wechat.checkQRStatus(qrTicket.value)

      if (result.code === 200) {
        const status = result.data.status
        loginStatus.value = status

        if (status === 'confirmed') {
          // 登录成功
          clearInterval(statusCheckTimer)
          await handleWechatLoginSuccess(result.data)
        } else if (status === 'expired') {
          // 二维码过期
          clearInterval(statusCheckTimer)
        }
      }
    } catch (error) {
      console.error('检查登录状态失败:', error)
    }
  }, 2000)
}

// 处理微信登录成功
async function handleWechatLoginSuccess(data) {
  try {
    await userStore.wechatLogin(data)
    
    // 跳转到目标页面或首页
    const redirectPath = sessionStorage.getItem('redirectPath') || '/'
    sessionStorage.removeItem('redirectPath')
    router.push(redirectPath)
  } catch (error) {
    console.error('微信登录失败:', error)
  }
}

// 发送短信验证码
async function sendSmsCode() {
  if (!isValidPhone.value) {
    ElMessage.error('请输入正确的手机号')
    return
  }

  try {
    smsLoading.value = true

    const result = await auth.sendSms({
      phone: phoneForm.phone
    })

    if (result.code === 200) {
      ElMessage.success('验证码发送成功')
      startSmsCountdown()
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    ElMessage.error(error.message || '发送验证码失败')
  } finally {
    smsLoading.value = false
  }
}

// 开始短信倒计时
function startSmsCountdown() {
  smsCountdown.value = 60
  smsTimer = setInterval(() => {
    smsCountdown.value--
    if (smsCountdown.value <= 0) {
      clearInterval(smsTimer)
    }
  }, 1000)
}

// 处理手机号登录
async function handlePhoneLogin() {
  try {
    await phoneFormRef.value.validate()
    phoneLoading.value = true
    
    await userStore.login({
      phone: phoneForm.phone,
      code: phoneForm.code,
      inviteCode: phoneForm.inviteCode || undefined
    })
    
    // 跳转到目标页面或首页
    const redirectPath = sessionStorage.getItem('redirectPath') || '/'
    sessionStorage.removeItem('redirectPath')
    router.push(redirectPath)
  } catch (error) {
    console.error('手机登录失败:', error)
  } finally {
    phoneLoading.value = false
  }
}

// 组件挂载时生成二维码
onMounted(() => {
  if (loginType.value === 'wechat') {
    generateQRCode()
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (statusCheckTimer) {
    clearInterval(statusCheckTimer)
  }
  if (smsTimer) {
    clearInterval(smsTimer)
  }
})
</script>

<style lang="scss" scoped>
.login-form {
  .form-header {
    text-align: center;
    margin-bottom: 32px;
    
    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #333;
    }
    
    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }
  
  .login-tabs {
    margin-bottom: 24px;
    
    :deep(.el-tabs__header) {
      margin: 0 0 24px 0;
    }
    
    :deep(.el-tabs__nav-wrap::after) {
      display: none;
    }
  }
  
  .wechat-login {
    text-align: center;
    
    .qr-container {
      margin-bottom: 24px;
      
      .qr-loading,
      .qr-error {
        padding: 40px 20px;
        color: #666;
        
        .el-icon {
          font-size: 32px;
          margin-bottom: 16px;
        }
        
        p {
          margin: 0 0 16px 0;
        }
      }
      
      .qr-code {
        canvas {
          border: 1px solid #e4e7ed;
          border-radius: 8px;
        }
        
        .qr-tip {
          margin: 16px 0 0 0;
          color: #666;
          font-size: 14px;
        }
      }
    }
    
    .login-status {
      .status-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 12px;
        border-radius: 8px;
        background: #f0f9ff;
        color: #1890ff;
        
        &.error {
          background: #fef2f2;
          color: #ef4444;
        }
        
        .el-icon {
          font-size: 16px;
        }
      }
    }
  }
  
  .phone-form {
    .code-input {
      display: flex;
      gap: 12px;
      
      .el-input {
        flex: 1;
      }
      
      .sms-btn {
        flex-shrink: 0;
        min-width: 120px;
      }
    }
    
    .login-btn {
      width: 100%;
    }
  }
  
  .form-footer {
    text-align: center;
    margin-top: 16px;
    
    .invite-toggle {
      font-size: 14px;
    }
  }
  
  .agreement {
    text-align: center;
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #e4e7ed;
    
    p {
      margin: 0;
      font-size: 12px;
      color: #999;
      
      a {
        color: #1890ff;
        text-decoration: none;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}
</style>
