<template>
  <div class="register-form">
    <!-- 标题 -->
    <div class="form-header">
      <h2>注册账号</h2>
      <p>加入我们，开启知识之旅</p>
    </div>

    <!-- 注册表单 -->
    <el-form 
      ref="formRef" 
      :model="form" 
      :rules="rules"
      class="register-form-content"
      @submit.prevent="handleRegister"
    >
      <el-form-item prop="phone">
        <el-input
          v-model="form.phone"
          placeholder="请输入手机号"
          size="large"
          maxlength="11"
          clearable
        >
          <template #prefix>
            <el-icon><Phone /></el-icon>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item prop="code">
        <div class="code-input">
          <el-input
            v-model="form.code"
            placeholder="请输入验证码"
            size="large"
            maxlength="6"
            clearable
          >
            <template #prefix>
              <el-icon><Message /></el-icon>
            </template>
          </el-input>
          <el-button
            :disabled="smsCountdown > 0 || !isValidPhone"
            :loading="smsLoading"
            @click="sendSmsCode"
            class="sms-btn"
            size="large"
          >
            {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
          </el-button>
        </div>
      </el-form-item>

      <el-form-item prop="nickname">
        <el-input
          v-model="form.nickname"
          placeholder="请输入昵称"
          size="large"
          maxlength="20"
          clearable
        >
          <template #prefix>
            <el-icon><User /></el-icon>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item prop="inviteCode">
        <el-input
          v-model="form.inviteCode"
          placeholder="邀请码（选填）"
          size="large"
          clearable
        >
          <template #prefix>
            <el-icon><Gift /></el-icon>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item prop="agreement">
        <el-checkbox v-model="form.agreement" size="large">
          我已阅读并同意
          <router-link to="/terms" target="_blank">《服务条款》</router-link>
          和
          <router-link to="/privacy" target="_blank">《隐私政策》</router-link>
        </el-checkbox>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          size="large"
          :loading="loading"
          @click="handleRegister"
          class="register-btn"
        >
          注册
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 登录链接 -->
    <div class="form-footer">
      <p>
        已有账号？
        <router-link to="/auth/login" class="login-link">立即登录</router-link>
      </p>
    </div>

    <!-- 注册福利提示 -->
    <div class="register-benefits">
      <h3>注册即享</h3>
      <div class="benefits-list">
        <div class="benefit-item">
          <el-icon><Gift /></el-icon>
          <span>新用户注册奖励 50 积分</span>
        </div>
        <div class="benefit-item">
          <el-icon><Star /></el-icon>
          <span>免费体验精选内容</span>
        </div>
        <div class="benefit-item">
          <el-icon><Trophy /></el-icon>
          <span>每日签到获取积分</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { auth } from '@/api'
import { ElMessage } from 'element-plus'
import {
  Phone,
  Message,
  User,
  Gift,
  Star,
  Trophy
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 表单数据
const formRef = ref(null)
const form = reactive({
  phone: '',
  code: '',
  nickname: '',
  inviteCode: '',
  agreement: false
})

// 状态
const loading = ref(false)
const smsLoading = ref(false)
const smsCountdown = ref(0)
let smsTimer = null

// 表单验证规则
const rules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '昵称长度为2-20个字符', trigger: 'blur' }
  ],
  agreement: [
    { 
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请阅读并同意服务条款和隐私政策'))
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ]
}

// 计算属性
const isValidPhone = computed(() => {
  return /^1[3-9]\d{9}$/.test(form.phone)
})

// 发送短信验证码
async function sendSmsCode() {
  if (!isValidPhone.value) {
    ElMessage.error('请输入正确的手机号')
    return
  }

  try {
    smsLoading.value = true

    const result = await auth.sendSms({
      phone: form.phone
    })

    if (result.code === 200) {
      ElMessage.success('验证码发送成功')
      startSmsCountdown()
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    ElMessage.error(error.message || '发送验证码失败')
  } finally {
    smsLoading.value = false
  }
}

// 开始短信倒计时
function startSmsCountdown() {
  smsCountdown.value = 60
  smsTimer = setInterval(() => {
    smsCountdown.value--
    if (smsCountdown.value <= 0) {
      clearInterval(smsTimer)
    }
  }, 1000)
}

// 处理注册
async function handleRegister() {
  try {
    await formRef.value.validate()
    loading.value = true
    
    await userStore.register({
      phone: form.phone,
      code: form.code,
      nickname: form.nickname,
      inviteCode: form.inviteCode || undefined
    })
    
    ElMessage.success('注册成功！')
    
    // 跳转到目标页面或首页
    const redirectPath = sessionStorage.getItem('redirectPath') || '/'
    sessionStorage.removeItem('redirectPath')
    router.push(redirectPath)
  } catch (error) {
    console.error('注册失败:', error)
  } finally {
    loading.value = false
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (smsTimer) {
    clearInterval(smsTimer)
  }
})
</script>

<style lang="scss" scoped>
.register-form {
  .form-header {
    text-align: center;
    margin-bottom: 32px;
    
    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #333;
    }
    
    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }
  
  .register-form-content {
    margin-bottom: 24px;
    
    .code-input {
      display: flex;
      gap: 12px;
      
      .el-input {
        flex: 1;
      }
      
      .sms-btn {
        flex-shrink: 0;
        min-width: 120px;
      }
    }
    
    .register-btn {
      width: 100%;
    }
    
    :deep(.el-checkbox) {
      .el-checkbox__label {
        font-size: 14px;
        color: #666;
        
        a {
          color: #1890ff;
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
  
  .form-footer {
    text-align: center;
    margin-bottom: 32px;
    
    p {
      margin: 0;
      color: #666;
      font-size: 14px;
      
      .login-link {
        color: #1890ff;
        text-decoration: none;
        font-weight: 500;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
  
  .register-benefits {
    padding: 24px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    
    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      text-align: center;
    }
    
    .benefits-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
      
      .benefit-item {
        display: flex;
        align-items: center;
        gap: 12px;
        color: #666;
        font-size: 14px;
        
        .el-icon {
          color: #1890ff;
          font-size: 16px;
          flex-shrink: 0;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .register-form {
    .register-benefits {
      padding: 16px;
      
      .benefits-list {
        .benefit-item {
          font-size: 13px;
          
          .el-icon {
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>
