<template>
  <div class="search-page">
    <div class="container">
      <!-- 搜索框 -->
      <div class="search-header">
        <div class="search-box">
          <el-input
            v-model="searchQuery"
            placeholder="搜索内容、标题、关键词..."
            size="large"
            clearable
            @keyup.enter="handleSearch"
            @clear="handleClear"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #suffix>
              <el-button 
                type="primary" 
                @click="handleSearch"
                :loading="loading"
                size="small"
              >
                搜索
              </el-button>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 搜索建议/历史 -->
      <div v-if="!hasSearched" class="search-suggestions">
        <!-- 搜索历史 -->
        <div v-if="searchHistory.length > 0" class="history-section">
          <div class="section-header">
            <h3>搜索历史</h3>
            <el-button text @click="clearHistory">
              <el-icon><Delete /></el-icon>
              清空
            </el-button>
          </div>
          <div class="history-tags">
            <el-tag
              v-for="(item, index) in searchHistory"
              :key="index"
              @click="searchQuery = item; handleSearch()"
              @close="removeHistoryItem(index)"
              closable
              class="history-tag"
            >
              {{ item }}
            </el-tag>
          </div>
        </div>

        <!-- 热门搜索 -->
        <div class="hot-section">
          <div class="section-header">
            <h3>热门搜索</h3>
          </div>
          <div class="hot-tags">
            <el-tag
              v-for="(item, index) in hotSearches"
              :key="index"
              @click="searchQuery = item; handleSearch()"
              :type="index < 3 ? 'danger' : 'info'"
              class="hot-tag"
            >
              <span class="rank">{{ index + 1 }}</span>
              {{ item }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 搜索结果 -->
      <div v-if="hasSearched" class="search-results">
        <!-- 结果统计 -->
        <div class="result-stats">
          <span v-if="!loading">
            找到 <strong>{{ total }}</strong> 个相关结果
            <span v-if="searchQuery">"<em>{{ searchQuery }}</em>"</span>
          </span>
        </div>

        <!-- 筛选栏 -->
        <div class="filter-bar">
          <div class="filter-left">
            <el-select 
              v-model="filters.contentType" 
              placeholder="内容类型"
              clearable
              @change="handleFilterChange"
            >
              <el-option label="全部类型" value="" />
              <el-option label="文章" value="article" />
              <el-option label="网盘资源" value="netdisk" />
              <el-option label="视频课程" value="video" />
              <el-option label="文档资料" value="document" />
            </el-select>

            <el-select 
              v-model="filters.unlockType" 
              placeholder="解锁方式"
              clearable
              @change="handleFilterChange"
            >
              <el-option label="全部方式" value="" />
              <el-option label="免费内容" value="free" />
              <el-option label="积分解锁" value="points" />
              <el-option label="VIP专享" value="vip" />
            </el-select>
          </div>

          <div class="filter-right">
            <el-select 
              v-model="sortBy" 
              @change="handleSortChange"
            >
              <el-option label="相关度" value="relevance" />
              <el-option label="最新发布" value="latest" />
              <el-option label="最多浏览" value="views" />
              <el-option label="最多点赞" value="likes" />
            </el-select>
          </div>
        </div>

        <!-- 搜索结果列表 -->
        <div class="result-list">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="5" animated />
          </div>

          <!-- 结果列表 -->
          <div v-else-if="searchResults.length > 0" class="results">
            <div 
              v-for="item in searchResults" 
              :key="item.id"
              class="result-item"
              @click="$router.push(`/content/${item.id}`)"
            >
              <div class="result-content">
                <h3 class="title" v-html="highlightKeyword(item.title)"></h3>
                <p class="summary" v-html="highlightKeyword(item.summary)"></p>
                <div class="meta">
                  <span class="type">{{ getContentTypeText(item.contentType) }}</span>
                  <span class="unlock">{{ getUnlockTypeText(item.unlockType, item.unlockPrice) }}</span>
                  <span class="stats">{{ item.viewCount }} 浏览</span>
                  <span class="time">{{ formatTime(item.createdAt) }}</span>
                </div>
              </div>
              <div v-if="item.coverImage" class="result-image">
                <img :src="item.coverImage" :alt="item.title" />
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <el-empty description="没有找到相关内容">
              <el-button type="primary" @click="clearSearch">
                重新搜索
              </el-button>
            </el-empty>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="!loading && searchResults.length > 0" class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            layout="prev, pager, next"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { content } from '@/api'
import { ElMessage } from 'element-plus'
import { Search, Delete } from '@element-plus/icons-vue'
import { formatTime } from '@/utils'

const route = useRoute()
const router = useRouter()

// 状态
const loading = ref(false)
const searchQuery = ref('')
const hasSearched = ref(false)
const searchResults = ref([])
const searchHistory = ref([])
const hotSearches = ref([
  'Vue3教程', 'JavaScript进阶', 'Python爬虫', 
  '数据结构', '算法题解', 'React实战',
  '前端面试', '后端开发', '数据库设计'
])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const sortBy = ref('relevance')

// 筛选条件
const filters = reactive({
  contentType: '',
  unlockType: ''
})

// 初始化
onMounted(() => {
  loadSearchHistory()
  initFromQuery()
  if (searchQuery.value) {
    performSearch()
  }
})

// 监听路由变化
watch(() => route.query, () => {
  initFromQuery()
  if (searchQuery.value) {
    performSearch()
  }
})

// 从URL参数初始化
function initFromQuery() {
  searchQuery.value = route.query.q || ''
  filters.contentType = route.query.type || ''
  filters.unlockType = route.query.unlock || ''
  sortBy.value = route.query.sort || 'relevance'
  currentPage.value = parseInt(route.query.page) || 1
  hasSearched.value = !!searchQuery.value
}

// 加载搜索历史
function loadSearchHistory() {
  const history = localStorage.getItem('searchHistory')
  if (history) {
    searchHistory.value = JSON.parse(history).slice(0, 10)
  }
}

// 保存搜索历史
function saveSearchHistory(query) {
  if (!query.trim()) return
  
  const history = [...searchHistory.value]
  const index = history.indexOf(query)
  
  if (index > -1) {
    history.splice(index, 1)
  }
  
  history.unshift(query)
  searchHistory.value = history.slice(0, 10)
  
  localStorage.setItem('searchHistory', JSON.stringify(searchHistory.value))
}

// 执行搜索
function handleSearch() {
  if (!searchQuery.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  
  saveSearchHistory(searchQuery.value)
  currentPage.value = 1
  hasSearched.value = true
  updateQuery()
  performSearch()
}

// 实际搜索请求
async function performSearch() {
  if (!searchQuery.value.trim()) return
  
  try {
    loading.value = true
    
    const params = {
      keyword: searchQuery.value,
      page: currentPage.value,
      pageSize: pageSize.value,
      sort: sortBy.value,
      contentType: filters.contentType || undefined,
      unlockType: filters.unlockType || undefined
    }

    const result = await content.search(params)
    if (result.code === 200) {
      searchResults.value = result.data.list
      total.value = result.data.total
    }
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    loading.value = false
  }
}

// 清空搜索
function handleClear() {
  searchQuery.value = ''
  hasSearched.value = false
  searchResults.value = []
  updateQuery()
}

// 清空搜索重新开始
function clearSearch() {
  handleClear()
}

// 处理筛选变化
function handleFilterChange() {
  currentPage.value = 1
  updateQuery()
  performSearch()
}

// 处理排序变化
function handleSortChange() {
  currentPage.value = 1
  updateQuery()
  performSearch()
}

// 处理页码变化
function handlePageChange(page) {
  currentPage.value = page
  updateQuery()
  performSearch()
}

// 清空搜索历史
function clearHistory() {
  searchHistory.value = []
  localStorage.removeItem('searchHistory')
}

// 删除单个历史记录
function removeHistoryItem(index) {
  searchHistory.value.splice(index, 1)
  localStorage.setItem('searchHistory', JSON.stringify(searchHistory.value))
}

// 高亮关键词
function highlightKeyword(text) {
  if (!text || !searchQuery.value) return text
  
  const keyword = searchQuery.value.trim()
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 获取内容类型文本
function getContentTypeText(type) {
  const typeMap = {
    article: '文章',
    netdisk: '网盘',
    video: '视频',
    document: '文档'
  }
  return typeMap[type] || '文章'
}

// 获取解锁类型文本
function getUnlockTypeText(type, price) {
  if (type === 'free') return '免费'
  if (type === 'vip') return 'VIP专享'
  if (type === 'points') return `${price}积分`
  if (type === 'card') return '卡密解锁'
  return '免费'
}

// 更新URL参数
function updateQuery() {
  const query = {}
  
  if (searchQuery.value) query.q = searchQuery.value
  if (filters.contentType) query.type = filters.contentType
  if (filters.unlockType) query.unlock = filters.unlockType
  if (sortBy.value !== 'relevance') query.sort = sortBy.value
  if (currentPage.value > 1) query.page = currentPage.value

  router.push({ query })
}
</script>

<style lang="scss" scoped>
.search-page {
  min-height: 100vh;
  background: #f8fafc;
  padding: 40px 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.search-header {
  margin-bottom: 32px;
  
  .search-box {
    :deep(.el-input) {
      .el-input__wrapper {
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      }
    }
  }
}

.search-suggestions {
  .history-section,
  .hot-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }
    
    .history-tags,
    .hot-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .el-tag {
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          transform: translateY(-1px);
        }
      }
      
      .hot-tag {
        .rank {
          display: inline-block;
          width: 16px;
          height: 16px;
          line-height: 16px;
          text-align: center;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          font-size: 10px;
          margin-right: 4px;
        }
      }
    }
  }
}

.search-results {
  .result-stats {
    margin-bottom: 16px;
    color: #666;
    font-size: 14px;
    
    strong {
      color: #333;
    }
    
    em {
      color: #1890ff;
      font-style: normal;
    }
  }
  
  .filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    gap: 16px;
    
    .filter-left {
      display: flex;
      gap: 12px;
    }
  }
  
  .result-list {
    .results {
      .result-item {
        display: flex;
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 16px;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }
        
        .result-content {
          flex: 1;
          
          .title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0 0 8px 0;
            line-height: 1.4;
            
            :deep(mark) {
              background: #fff3cd;
              color: #856404;
              padding: 2px 4px;
              border-radius: 4px;
            }
          }
          
          .summary {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
            margin: 0 0 12px 0;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            
            :deep(mark) {
              background: #fff3cd;
              color: #856404;
              padding: 2px 4px;
              border-radius: 4px;
            }
          }
          
          .meta {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #999;
            
            span {
              display: flex;
              align-items: center;
              gap: 4px;
            }
            
            .type {
              color: #1890ff;
              font-weight: 500;
            }
            
            .unlock {
              color: #f56565;
            }
          }
        }
        
        .result-image {
          flex-shrink: 0;
          width: 120px;
          height: 80px;
          margin-left: 16px;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
          }
        }
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 60px 20px;
    }
  }
  
  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 32px;
  }
}

.loading-container {
  padding: 20px;
}

// 响应式设计
@media (max-width: 768px) {
  .search-page {
    padding: 20px 0;
  }
  
  .search-header {
    margin-bottom: 24px;
  }
  
  .search-suggestions {
    .history-section,
    .hot-section {
      padding: 16px;
      margin-bottom: 16px;
    }
  }
  
  .search-results {
    .filter-bar {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
    }
    
    .result-list {
      .results {
        .result-item {
          flex-direction: column;
          padding: 16px;
          
          .result-image {
            width: 100%;
            height: 120px;
            margin-left: 0;
            margin-top: 12px;
          }
        }
      }
    }
  }
}
</style>
