<template>
  <div class="not-found">
    <div class="container">
      <div class="content">
        <div class="error-code">404</div>
        <h1 class="title">页面不存在</h1>
        <p class="description">
          抱歉，您访问的页面不存在或已被删除
        </p>
        <div class="actions">
          <el-button type="primary" @click="goHome">
            <el-icon><HomeFilled /></el-icon>
            返回首页
          </el-button>
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回上页
          </el-button>
        </div>
      </div>
      <div class="illustration">
        <el-icon class="icon"><QuestionFilled /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { HomeFilled, ArrowLeft, QuestionFilled } from '@element-plus/icons-vue'

const router = useRouter()

function goHome() {
  router.push('/')
}

function goBack() {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.container {
  display: flex;
  align-items: center;
  gap: 60px;
  max-width: 800px;
  width: 100%;
}

.content {
  flex: 1;
  
  .error-code {
    font-size: 120px;
    font-weight: bold;
    color: #1890ff;
    line-height: 1;
    margin-bottom: 20px;
  }
  
  .title {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    margin: 0 0 16px 0;
  }
  
  .description {
    font-size: 16px;
    color: #666;
    margin: 0 0 32px 0;
    line-height: 1.6;
  }
  
  .actions {
    display: flex;
    gap: 16px;
  }
}

.illustration {
  flex-shrink: 0;
  
  .icon {
    font-size: 200px;
    color: #e6f7ff;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    text-align: center;
    gap: 40px;
  }
  
  .content {
    .error-code {
      font-size: 80px;
    }
    
    .title {
      font-size: 24px;
    }
    
    .actions {
      justify-content: center;
      flex-wrap: wrap;
    }
  }
  
  .illustration {
    .icon {
      font-size: 120px;
    }
  }
}
</style>
