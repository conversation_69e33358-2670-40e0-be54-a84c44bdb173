<template>
  <div class="terms-page">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>服务条款</h1>
        <p class="update-time">最后更新时间：2023年12月</p>
      </div>

      <!-- 条款内容 -->
      <div class="terms-content">
        <section>
          <h2>1. 服务说明</h2>
          <p>
            知识付费平台（以下简称"本平台"）是一个提供优质学习内容和知识服务的平台。
            通过注册和使用本平台，您同意遵守本服务条款的所有规定。
          </p>
        </section>

        <section>
          <h2>2. 用户注册</h2>
          <ul>
            <li>您必须年满18周岁或在法定监护人同意下使用本服务</li>
            <li>注册时提供的信息必须真实、准确、完整</li>
            <li>您有责任维护账户安全，不得与他人共享账户</li>
            <li>一个手机号只能注册一个账户</li>
          </ul>
        </section>

        <section>
          <h2>3. 用户行为规范</h2>
          <p>使用本平台时，您不得：</p>
          <ul>
            <li>发布违法、有害、威胁、辱骂、诽谤等内容</li>
            <li>侵犯他人知识产权或其他合法权益</li>
            <li>传播病毒、恶意代码或进行网络攻击</li>
            <li>利用技术手段恶意刷取积分或其他虚拟物品</li>
            <li>进行任何可能损害平台正常运营的行为</li>
          </ul>
        </section>

        <section>
          <h2>4. 内容与知识产权</h2>
          <ul>
            <li>平台上的所有内容均受知识产权法保护</li>
            <li>用户仅获得内容的使用权，不得转载、分发或商业使用</li>
            <li>用户上传的内容必须拥有合法权利</li>
            <li>平台有权审核、编辑或删除不当内容</li>
          </ul>
        </section>

        <section>
          <h2>5. 积分与VIP服务</h2>
          <ul>
            <li>积分是平台内的虚拟货币，不可兑换现金</li>
            <li>积分获取方式包括签到、邀请、观看广告等</li>
            <li>VIP服务提供额外权益，具体以页面说明为准</li>
            <li>虚拟物品一经使用不可退还</li>
          </ul>
        </section>

        <section>
          <h2>6. 隐私保护</h2>
          <p>
            我们重视您的隐私保护。关于个人信息的收集、使用和保护，
            请参阅我们的《隐私政策》。
          </p>
        </section>

        <section>
          <h2>7. 免责声明</h2>
          <ul>
            <li>平台内容仅供参考，不构成专业建议</li>
            <li>我们不保证服务的连续性和无错误性</li>
            <li>因不可抗力导致的服务中断，我们不承担责任</li>
            <li>用户因使用平台内容产生的后果自行承担</li>
          </ul>
        </section>

        <section>
          <h2>8. 服务变更与终止</h2>
          <ul>
            <li>我们有权随时修改、暂停或终止服务</li>
            <li>重大变更会提前通知用户</li>
            <li>用户违反条款时，我们有权限制或终止其账户</li>
            <li>服务终止后，相关数据可能被删除</li>
          </ul>
        </section>

        <section>
          <h2>9. 争议解决</h2>
          <p>
            因使用本服务产生的争议，双方应友好协商解决。
            协商不成的，提交至平台所在地人民法院诉讼解决。
          </p>
        </section>

        <section>
          <h2>10. 条款修改</h2>
          <p>
            我们保留随时修改本服务条款的权利。修改后的条款将在平台上公布，
            继续使用服务即表示您接受修改后的条款。
          </p>
        </section>

        <section>
          <h2>11. 联系方式</h2>
          <p>如果您对本服务条款有任何疑问，请联系我们：</p>
          <div class="contact-info">
            <p><strong>客服邮箱：</strong><EMAIL></p>
            <p><strong>客服电话：</strong>400-123-4567</p>
            <p><strong>服务时间：</strong>周一至周五 9:00-18:00</p>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<script setup>
// 页面元数据
document.title = '服务条款 - 知识付费平台'
</script>

<style lang="scss" scoped>
.terms-page {
  min-height: 100vh;
  background: #f8fafc;
  padding: 40px 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  
  h1 {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
  }
  
  .update-time {
    font-size: 14px;
    color: #666;
    margin: 0;
  }
}

.terms-content {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  
  section {
    margin-bottom: 32px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    h2 {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #1890ff;
    }
    
    p {
      font-size: 15px;
      line-height: 1.7;
      color: #555;
      margin: 0 0 12px 0;
    }
    
    ul {
      margin: 12px 0;
      padding-left: 20px;
      
      li {
        font-size: 15px;
        line-height: 1.7;
        color: #555;
        margin-bottom: 8px;
      }
    }
    
    .contact-info {
      background: #f8fafc;
      padding: 20px;
      border-radius: 8px;
      margin-top: 16px;
      
      p {
        margin: 0 0 8px 0;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        strong {
          color: #333;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .terms-page {
    padding: 20px 0;
  }
  
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      font-size: 24px;
    }
  }
  
  .terms-content {
    padding: 24px 20px;
    
    section {
      margin-bottom: 24px;
      
      h2 {
        font-size: 18px;
      }
      
      p, li {
        font-size: 14px;
      }
    }
  }
}
</style>
