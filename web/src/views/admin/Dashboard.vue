<template>
  <div class="dashboard">
    <!-- 数据概览 -->
    <div class="stats-overview">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon user-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.totalUsers }}</div>
                <div class="stat-label">总用户数</div>
                <div class="stat-change" :class="{ positive: stats.userGrowth > 0 }">
                  <el-icon><TrendCharts /></el-icon>
                  {{ stats.userGrowth > 0 ? '+' : '' }}{{ stats.userGrowth }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon content-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.totalContents }}</div>
                <div class="stat-label">内容总数</div>
                <div class="stat-change" :class="{ positive: stats.contentGrowth > 0 }">
                  <el-icon><TrendCharts /></el-icon>
                  {{ stats.contentGrowth > 0 ? '+' : '' }}{{ stats.contentGrowth }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon revenue-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">¥{{ formatNumber(stats.totalRevenue) }}</div>
                <div class="stat-label">总收入</div>
                <div class="stat-change" :class="{ positive: stats.revenueGrowth > 0 }">
                  <el-icon><TrendCharts /></el-icon>
                  {{ stats.revenueGrowth > 0 ? '+' : '' }}{{ stats.revenueGrowth }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon vip-icon">
                <el-icon><Crown /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.vipUsers }}</div>
                <div class="stat-label">VIP用户</div>
                <div class="stat-change" :class="{ positive: stats.vipGrowth > 0 }">
                  <el-icon><TrendCharts /></el-icon>
                  {{ stats.vipGrowth > 0 ? '+' : '' }}{{ stats.vipGrowth }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 用户增长趋势 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card>
            <template #header>
              <div class="chart-header">
                <h3>用户增长趋势</h3>
                <el-select v-model="userChartPeriod" size="small" @change="loadUserChart">
                  <el-option label="最近7天" value="7d" />
                  <el-option label="最近30天" value="30d" />
                  <el-option label="最近90天" value="90d" />
                </el-select>
              </div>
            </template>
            <div ref="userChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
        
        <!-- 收入统计 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card>
            <template #header>
              <div class="chart-header">
                <h3>收入统计</h3>
                <el-select v-model="revenueChartPeriod" size="small" @change="loadRevenueChart">
                  <el-option label="最近7天" value="7d" />
                  <el-option label="最近30天" value="30d" />
                  <el-option label="最近90天" value="90d" />
                </el-select>
              </div>
            </template>
            <div ref="revenueChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格区域 -->
    <div class="tables-section">
      <el-row :gutter="20">
        <!-- 热门内容 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card>
            <template #header>
              <div class="table-header">
                <h3>热门内容</h3>
                <el-button text type="primary" @click="$router.push('/admin/content/list')">
                  查看更多
                </el-button>
              </div>
            </template>
            
            <el-table :data="hotContents" style="width: 100%" size="small">
              <el-table-column prop="title" label="标题" show-overflow-tooltip />
              <el-table-column prop="viewCount" label="浏览量" width="80" />
              <el-table-column prop="unlockCount" label="解锁数" width="80" />
            </el-table>
          </el-card>
        </el-col>
        
        <!-- 最新用户 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card>
            <template #header>
              <div class="table-header">
                <h3>最新用户</h3>
                <el-button text type="primary" @click="$router.push('/admin/users/list')">
                  查看更多
                </el-button>
              </div>
            </template>
            
            <el-table :data="recentUsers" style="width: 100%" size="small">
              <el-table-column prop="nickname" label="昵称" show-overflow-tooltip />
              <el-table-column label="状态" width="80">
                <template #default="{ row }">
                  <el-tag v-if="row.isVip" type="warning" size="small">VIP</el-tag>
                  <el-tag v-else type="info" size="small">普通</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createdAt" label="注册时间" width="100">
                <template #default="{ row }">
                  {{ formatDate(row.createdAt) }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 系统状态 -->
    <div class="system-status">
      <el-card>
        <template #header>
          <h3>系统状态</h3>
        </template>
        
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="6" :lg="6">
            <div class="status-item">
              <div class="status-label">服务器状态</div>
              <div class="status-value">
                <el-tag type="success">正常</el-tag>
              </div>
            </div>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="6" :lg="6">
            <div class="status-item">
              <div class="status-label">数据库状态</div>
              <div class="status-value">
                <el-tag type="success">正常</el-tag>
              </div>
            </div>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="6" :lg="6">
            <div class="status-item">
              <div class="status-label">存储空间</div>
              <div class="status-value">
                <span>{{ systemStatus.storageUsed }}GB / {{ systemStatus.storageTotal }}GB</span>
              </div>
            </div>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="6" :lg="6">
            <div class="status-item">
              <div class="status-label">在线用户</div>
              <div class="status-value">
                <span>{{ systemStatus.onlineUsers }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { admin } from '@/api'
import { formatTime } from '@/utils'
import * as echarts from 'echarts'
import {
  User,
  Document,
  Money,
  Crown,
  TrendCharts
} from '@element-plus/icons-vue'

const router = useRouter()

// 图表引用
const userChartRef = ref(null)
const revenueChartRef = ref(null)

// 图表实例
let userChart = null
let revenueChart = null

// 状态
const userChartPeriod = ref('30d')
const revenueChartPeriod = ref('30d')

// 统计数据
const stats = reactive({
  totalUsers: 0,
  totalContents: 0,
  totalRevenue: 0,
  vipUsers: 0,
  userGrowth: 0,
  contentGrowth: 0,
  revenueGrowth: 0,
  vipGrowth: 0
})

// 热门内容
const hotContents = ref([])

// 最新用户
const recentUsers = ref([])

// 系统状态
const systemStatus = reactive({
  storageUsed: 0,
  storageTotal: 100,
  onlineUsers: 0
})

// 初始化
onMounted(async () => {
  await loadDashboardData()
  await nextTick()
  initCharts()
})

// 加载仪表盘数据
async function loadDashboardData() {
  try {
    const result = await admin.getDashboardData()
    if (result.code === 200) {
      const data = result.data
      
      // 更新统计数据
      Object.assign(stats, data.stats)
      
      // 更新热门内容
      hotContents.value = data.hotContents
      
      // 更新最新用户
      recentUsers.value = data.recentUsers
      
      // 更新系统状态
      Object.assign(systemStatus, data.systemStatus)
    }
  } catch (error) {
    console.error('加载仪表盘数据失败:', error)
  }
}

// 初始化图表
function initCharts() {
  // 初始化用户增长图表
  if (userChartRef.value) {
    userChart = echarts.init(userChartRef.value)
    loadUserChart()
  }
  
  // 初始化收入图表
  if (revenueChartRef.value) {
    revenueChart = echarts.init(revenueChartRef.value)
    loadRevenueChart()
  }
}

// 加载用户增长图表
async function loadUserChart() {
  try {
    const result = await admin.getUserChartData(userChartPeriod.value)
    if (result.code === 200) {
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: result.data.dates
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '新增用户',
          type: 'line',
          data: result.data.values,
          smooth: true,
          itemStyle: {
            color: '#1890ff'
          }
        }]
      }
      userChart.setOption(option)
    }
  } catch (error) {
    console.error('加载用户图表数据失败:', error)
  }
}

// 加载收入图表
async function loadRevenueChart() {
  try {
    const result = await admin.getRevenueChartData(revenueChartPeriod.value)
    if (result.code === 200) {
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: result.data.dates
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '收入',
          type: 'bar',
          data: result.data.values,
          itemStyle: {
            color: '#52c41a'
          }
        }]
      }
      revenueChart.setOption(option)
    }
  } catch (error) {
    console.error('加载收入图表数据失败:', error)
  }
}

// 格式化数字
function formatNumber(num) {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toLocaleString()
}

// 格式化日期
function formatDate(date) {
  return new Date(date).toLocaleDateString()
}
</script>

<style lang="scss" scoped>
.dashboard {
  .stats-overview {
    margin-bottom: 20px;
    
    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          
          .el-icon {
            font-size: 24px;
            color: white;
          }
          
          &.user-icon {
            background: #1890ff;
          }
          
          &.content-icon {
            background: #52c41a;
          }
          
          &.revenue-icon {
            background: #faad14;
          }
          
          &.vip-icon {
            background: #722ed1;
          }
        }
        
        .stat-info {
          flex: 1;
          
          .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 4px;
          }
          
          .stat-change {
            font-size: 12px;
            color: #999;
            display: flex;
            align-items: center;
            gap: 4px;
            
            &.positive {
              color: #52c41a;
            }
          }
        }
      }
    }
  }
  
  .charts-section {
    margin-bottom: 20px;
    
    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .chart-container {
      height: 300px;
    }
  }
  
  .tables-section {
    margin-bottom: 20px;
    
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
  
  .system-status {
    .status-item {
      text-align: center;
      padding: 16px;
      
      .status-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }
      
      .status-value {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard {
    .charts-section {
      .chart-container {
        height: 250px;
      }
    }
    
    .system-status {
      .status-item {
        padding: 12px;
        
        .status-label {
          font-size: 12px;
        }
        
        .status-value {
          font-size: 14px;
        }
      }
    }
  }
}
</style>
