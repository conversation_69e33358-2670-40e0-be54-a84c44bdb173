<template>
  <div class="users-list">
    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户名/昵称">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入用户名或昵称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="手机号">
          <el-input
            v-model="searchForm.phone"
            placeholder="请输入手机号"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="用户类型">
          <el-select v-model="searchForm.userType" placeholder="选择用户类型" clearable>
            <el-option label="全部用户" value="" />
            <el-option label="普通用户" value="normal" />
            <el-option label="VIP用户" value="vip" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="注册时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="正常" value="active" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="userList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :src="row.avatar" :size="40">
              {{ row.nickname?.charAt(0) }}
            </el-avatar>
          </template>
        </el-table-column>
        
        <el-table-column prop="username" label="用户名" width="120" show-overflow-tooltip />
        
        <el-table-column prop="nickname" label="昵称" width="120" show-overflow-tooltip />
        
        <el-table-column prop="phone" label="手机号" width="120" />
        
        <el-table-column label="用户类型" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.isVip" type="warning" size="small">
              <el-icon><Crown /></el-icon>
              VIP
            </el-tag>
            <el-tag v-else type="info" size="small">普通</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="积分" width="80">
          <template #default="{ row }">
            {{ row.points || 0 }}
          </template>
        </el-table-column>
        
        <el-table-column label="统计" width="120">
          <template #default="{ row }">
            <div class="stats">
              <div>解锁: {{ row.unlockCount || 0 }}</div>
              <div>邀请: {{ row.inviteCount || 0 }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
              {{ row.status === 'active' ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="注册时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="最后登录" width="150">
          <template #default="{ row }">
            {{ row.lastLoginAt ? formatTime(row.lastLoginAt) : '从未登录' }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button text type="primary" @click="handleView(row)">
              查看
            </el-button>
            <el-button text type="primary" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-dropdown @command="(command) => handleCommand(command, row)">
              <el-button text type="primary">
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="points">积分管理</el-dropdown-item>
                  <el-dropdown-item command="vip">VIP管理</el-dropdown-item>
                  <el-dropdown-item 
                    :command="row.status === 'active' ? 'disable' : 'enable'"
                    divided
                  >
                    {{ row.status === 'active' ? '禁用用户' : '启用用户' }}
                  </el-dropdown-item>
                  <el-dropdown-item command="delete">删除用户</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div v-if="selectedItems.length > 0" class="batch-actions">
        <span>已选择 {{ selectedItems.length }} 项</span>
        <el-button type="warning" @click="handleBatchDisable">批量禁用</el-button>
        <el-button type="success" @click="handleBatchEnable">批量启用</el-button>
        <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="showUserDetail"
      title="用户详情"
      width="600px"
    >
      <div v-if="currentUser" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="头像">
            <el-avatar :src="currentUser.avatar" :size="60">
              {{ currentUser.nickname?.charAt(0) }}
            </el-avatar>
          </el-descriptions-item>
          <el-descriptions-item label="用户名">
            {{ currentUser.username }}
          </el-descriptions-item>
          <el-descriptions-item label="昵称">
            {{ currentUser.nickname }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            {{ currentUser.phone }}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{ currentUser.email || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="性别">
            {{ getGenderText(currentUser.gender) }}
          </el-descriptions-item>
          <el-descriptions-item label="生日">
            {{ currentUser.birthday || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="积分">
            {{ currentUser.points || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="VIP状态">
            <el-tag v-if="currentUser.isVip" type="warning">
              VIP用户（到期：{{ formatTime(currentUser.vipExpireTime) }}）
            </el-tag>
            <el-tag v-else type="info">普通用户</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="邀请码">
            {{ currentUser.inviteCode }}
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">
            {{ formatTime(currentUser.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后登录">
            {{ currentUser.lastLoginAt ? formatTime(currentUser.lastLoginAt) : '从未登录' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 积分管理对话框 -->
    <el-dialog
      v-model="showPointsDialog"
      title="积分管理"
      width="400px"
    >
      <el-form :model="pointsForm" label-width="80px">
        <el-form-item label="当前积分">
          <span>{{ currentUser?.points || 0 }}</span>
        </el-form-item>
        <el-form-item label="操作类型">
          <el-radio-group v-model="pointsForm.type">
            <el-radio label="add">增加积分</el-radio>
            <el-radio label="subtract">扣除积分</el-radio>
            <el-radio label="set">设置积分</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="积分数量">
          <el-input-number
            v-model="pointsForm.amount"
            :min="0"
            :max="999999"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="操作原因">
          <el-input
            v-model="pointsForm.reason"
            type="textarea"
            placeholder="请输入操作原因"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showPointsDialog = false">取消</el-button>
        <el-button type="primary" @click="handlePointsSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { admin } from '@/api'
import { formatTime } from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Download,
  Crown,
  ArrowDown
} from '@element-plus/icons-vue'

// 状态
const loading = ref(false)
const userList = ref([])
const selectedItems = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const showUserDetail = ref(false)
const showPointsDialog = ref(false)
const currentUser = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  phone: '',
  userType: '',
  dateRange: [],
  status: ''
})

// 积分管理表单
const pointsForm = reactive({
  type: 'add',
  amount: 0,
  reason: ''
})

// 初始化
onMounted(() => {
  loadUserList()
})

// 加载用户列表
async function loadUserList() {
  try {
    loading.value = true
    
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      ...searchForm,
      startDate: searchForm.dateRange?.[0],
      endDate: searchForm.dateRange?.[1]
    }
    
    delete params.dateRange
    
    const result = await admin.getUserList(params)
    if (result.code === 200) {
      userList.value = result.data.list
      total.value = result.data.total
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索
function handleSearch() {
  currentPage.value = 1
  loadUserList()
}

// 处理重置
function handleReset() {
  Object.assign(searchForm, {
    keyword: '',
    phone: '',
    userType: '',
    dateRange: [],
    status: ''
  })
  currentPage.value = 1
  loadUserList()
}

// 处理导出
async function handleExport() {
  try {
    const result = await admin.exportUsers(searchForm)
    if (result.code === 200) {
      // 下载文件
      const link = document.createElement('a')
      link.href = result.data.downloadUrl
      link.download = '用户列表.xlsx'
      link.click()
      ElMessage.success('导出成功')
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 处理选择变化
function handleSelectionChange(selection) {
  selectedItems.value = selection
}

// 处理页码变化
function handlePageChange(page) {
  currentPage.value = page
  loadUserList()
}

// 处理页面大小变化
function handlePageSizeChange(size) {
  pageSize.value = size
  currentPage.value = 1
  loadUserList()
}

// 查看用户
async function handleView(row) {
  try {
    const result = await admin.getUserDetail(row.id)
    if (result.code === 200) {
      currentUser.value = result.data
      showUserDetail.value = true
    }
  } catch (error) {
    console.error('加载用户详情失败:', error)
    ElMessage.error('加载用户详情失败')
  }
}

// 编辑用户
function handleEdit(row) {
  // 这里可以打开编辑对话框或跳转到编辑页面
  ElMessage.info('编辑功能开发中')
}

// 处理命令
async function handleCommand(command, row) {
  currentUser.value = row
  
  switch (command) {
    case 'points':
      showPointsDialog.value = true
      break
    case 'vip':
      ElMessage.info('VIP管理功能开发中')
      break
    case 'disable':
      await handleDisableUser(row)
      break
    case 'enable':
      await handleEnableUser(row)
      break
    case 'delete':
      await handleDeleteUser(row)
      break
  }
}

// 禁用用户
async function handleDisableUser(row) {
  try {
    await ElMessageBox.confirm('确定要禁用此用户吗？', '确认禁用', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await admin.disableUser(row.id)
    ElMessage.success('禁用成功')
    loadUserList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('禁用失败:', error)
      ElMessage.error('禁用失败')
    }
  }
}

// 启用用户
async function handleEnableUser(row) {
  try {
    await admin.enableUser(row.id)
    ElMessage.success('启用成功')
    loadUserList()
  } catch (error) {
    console.error('启用失败:', error)
    ElMessage.error('启用失败')
  }
}

// 删除用户
async function handleDeleteUser(row) {
  try {
    await ElMessageBox.confirm('确定要删除此用户吗？删除后无法恢复！', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await admin.deleteUser(row.id)
    ElMessage.success('删除成功')
    loadUserList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 积分管理提交
async function handlePointsSubmit() {
  try {
    await admin.manageUserPoints(currentUser.value.id, pointsForm)
    ElMessage.success('积分操作成功')
    showPointsDialog.value = false
    
    // 重置表单
    Object.assign(pointsForm, {
      type: 'add',
      amount: 0,
      reason: ''
    })
    
    loadUserList()
  } catch (error) {
    console.error('积分操作失败:', error)
    ElMessage.error('积分操作失败')
  }
}

// 批量禁用
async function handleBatchDisable() {
  try {
    const ids = selectedItems.value.map(item => item.id)
    await admin.batchDisableUsers(ids)
    ElMessage.success('批量禁用成功')
    selectedItems.value = []
    loadUserList()
  } catch (error) {
    console.error('批量禁用失败:', error)
    ElMessage.error('批量禁用失败')
  }
}

// 批量启用
async function handleBatchEnable() {
  try {
    const ids = selectedItems.value.map(item => item.id)
    await admin.batchEnableUsers(ids)
    ElMessage.success('批量启用成功')
    selectedItems.value = []
    loadUserList()
  } catch (error) {
    console.error('批量启用失败:', error)
    ElMessage.error('批量启用失败')
  }
}

// 批量删除
async function handleBatchDelete() {
  try {
    await ElMessageBox.confirm('确定要删除选中的用户吗？删除后无法恢复！', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const ids = selectedItems.value.map(item => item.id)
    await admin.batchDeleteUsers(ids)
    ElMessage.success('批量删除成功')
    selectedItems.value = []
    loadUserList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 获取性别文本
function getGenderText(gender) {
  const genderMap = {
    0: '保密',
    1: '男',
    2: '女'
  }
  return genderMap[gender] || '保密'
}
</script>

<style lang="scss" scoped>
.users-list {
  .search-card {
    margin-bottom: 20px;
  }
  
  .table-card {
    .stats {
      font-size: 12px;
      color: #666;
      
      div {
        margin-bottom: 2px;
      }
    }
    
    .batch-actions {
      margin: 16px 0;
      padding: 12px;
      background: #f0f9ff;
      border-radius: 6px;
      display: flex;
      align-items: center;
      gap: 12px;
      
      span {
        color: #1890ff;
        font-weight: 500;
      }
    }
    
    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
  
  .user-detail {
    .el-descriptions {
      :deep(.el-descriptions__label) {
        width: 100px;
      }
    }
  }
}
</style>
