<template>
  <div class="admin-layout">
    <!-- 顶部导航栏 -->
    <div class="admin-header">
      <div class="header-left">
        <div class="logo">
          <el-icon><Setting /></el-icon>
          <span>管理后台</span>
        </div>
      </div>
      
      <div class="header-right">
        <!-- 通知 -->
        <el-badge :value="notificationCount" :hidden="notificationCount === 0">
          <el-button text @click="showNotifications = true">
            <el-icon><Bell /></el-icon>
          </el-button>
        </el-badge>
        
        <!-- 用户菜单 -->
        <el-dropdown @command="handleUserCommand">
          <div class="user-info">
            <el-avatar :src="adminInfo?.avatar" :size="32">
              {{ adminInfo?.username?.charAt(0) }}
            </el-avatar>
            <span class="username">{{ adminInfo?.username }}</span>
            <el-icon><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人设置</el-dropdown-item>
              <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="admin-main">
      <!-- 侧边栏 -->
      <div class="admin-sidebar" :class="{ collapsed: sidebarCollapsed }">
        <div class="sidebar-toggle">
          <el-button 
            text 
            @click="toggleSidebar"
            class="toggle-btn"
          >
            <el-icon>
              <Expand v-if="sidebarCollapsed" />
              <Fold v-else />
            </el-icon>
          </el-button>
        </div>
        
        <el-menu
          :default-active="currentRoute"
          mode="vertical"
          :collapse="sidebarCollapsed"
          @select="handleMenuSelect"
          class="admin-menu"
        >
          <!-- 仪表盘 -->
          <el-menu-item index="/admin">
            <el-icon><Odometer /></el-icon>
            <span>仪表盘</span>
          </el-menu-item>
          
          <!-- 内容管理 -->
          <el-sub-menu index="content">
            <template #title>
              <el-icon><Document /></el-icon>
              <span>内容管理</span>
            </template>
            <el-menu-item index="/admin/content/list">内容列表</el-menu-item>
            <el-menu-item index="/admin/content/create">发布内容</el-menu-item>
            <el-menu-item index="/admin/content/categories">分类管理</el-menu-item>
            <el-menu-item index="/admin/content/tags">标签管理</el-menu-item>
          </el-sub-menu>
          
          <!-- 用户管理 -->
          <el-sub-menu index="users">
            <template #title>
              <el-icon><User /></el-icon>
              <span>用户管理</span>
            </template>
            <el-menu-item index="/admin/users/list">用户列表</el-menu-item>
            <el-menu-item index="/admin/users/vip">VIP用户</el-menu-item>
            <el-menu-item index="/admin/users/invites">邀请记录</el-menu-item>
          </el-sub-menu>
          
          <!-- 积分管理 -->
          <el-sub-menu index="points">
            <template #title>
              <el-icon><CreditCard /></el-icon>
              <span>积分管理</span>
            </template>
            <el-menu-item index="/admin/points/records">积分记录</el-menu-item>
            <el-menu-item index="/admin/points/cards">卡密管理</el-menu-item>
            <el-menu-item index="/admin/points/settings">积分设置</el-menu-item>
          </el-sub-menu>
          
          <!-- 订单管理 -->
          <el-menu-item index="/admin/orders">
            <el-icon><ShoppingCart /></el-icon>
            <span>订单管理</span>
          </el-menu-item>
          
          <!-- 评论管理 -->
          <el-menu-item index="/admin/comments">
            <el-icon><ChatDotRound /></el-icon>
            <span>评论管理</span>
          </el-menu-item>
          
          <!-- 系统设置 -->
          <el-sub-menu index="system">
            <template #title>
              <el-icon><Setting /></el-icon>
              <span>系统设置</span>
            </template>
            <el-menu-item index="/admin/system/basic">基础设置</el-menu-item>
            <el-menu-item index="/admin/system/admins">管理员</el-menu-item>
            <el-menu-item index="/admin/system/logs">操作日志</el-menu-item>
          </el-sub-menu>
        </el-menu>
      </div>

      <!-- 页面内容 -->
      <div class="admin-content">
        <!-- 面包屑导航 -->
        <div class="content-header">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/admin' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item 
              v-for="item in breadcrumbs" 
              :key="item.path"
              :to="item.path ? { path: item.path } : null"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <!-- 页面主体 -->
        <div class="content-body">
          <router-view />
        </div>
      </div>
    </div>

    <!-- 通知抽屉 -->
    <el-drawer
      v-model="showNotifications"
      title="系统通知"
      direction="rtl"
      size="400px"
    >
      <div class="notifications-list">
        <div 
          v-for="notification in notifications" 
          :key="notification.id"
          class="notification-item"
          :class="{ unread: !notification.read }"
        >
          <div class="notification-content">
            <h4>{{ notification.title }}</h4>
            <p>{{ notification.content }}</p>
            <div class="notification-time">
              {{ formatTime(notification.createdAt) }}
            </div>
          </div>
          <div class="notification-actions">
            <el-button 
              v-if="!notification.read" 
              text 
              size="small"
              @click="markAsRead(notification.id)"
            >
              标记已读
            </el-button>
          </div>
        </div>
        
        <div v-if="notifications.length === 0" class="empty-notifications">
          <el-empty description="暂无通知" />
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { admin } from '@/api'
import { removeToken } from '@/utils/auth'
import { formatTime } from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Setting,
  Bell,
  ArrowDown,
  Expand,
  Fold,
  Odometer,
  Document,
  User,
  CreditCard,
  ShoppingCart,
  ChatDotRound
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 状态
const sidebarCollapsed = ref(false)
const showNotifications = ref(false)
const notificationCount = ref(0)

// 管理员信息
const adminInfo = reactive({
  username: '',
  avatar: '',
  role: ''
})

// 通知列表
const notifications = ref([])

// 计算属性
const currentRoute = computed(() => route.path)

const breadcrumbs = computed(() => {
  const pathSegments = route.path.split('/').filter(Boolean)
  const breadcrumbs = []
  
  // 根据路径生成面包屑
  if (pathSegments.length > 1) {
    const routeMap = {
      'content': '内容管理',
      'users': '用户管理',
      'points': '积分管理',
      'orders': '订单管理',
      'comments': '评论管理',
      'system': '系统设置',
      'list': '列表',
      'create': '创建',
      'categories': '分类管理',
      'tags': '标签管理',
      'vip': 'VIP用户',
      'invites': '邀请记录',
      'records': '记录',
      'cards': '卡密管理',
      'settings': '设置',
      'basic': '基础设置',
      'admins': '管理员',
      'logs': '操作日志'
    }
    
    for (let i = 1; i < pathSegments.length; i++) {
      const segment = pathSegments[i]
      const title = routeMap[segment] || segment
      breadcrumbs.push({
        title,
        path: i === pathSegments.length - 1 ? null : `/${pathSegments.slice(0, i + 1).join('/')}`
      })
    }
  }
  
  return breadcrumbs
})

// 初始化
onMounted(() => {
  loadAdminInfo()
  loadNotifications()
})

// 加载管理员信息
async function loadAdminInfo() {
  try {
    const result = await admin.getProfile()
    if (result.code === 200) {
      Object.assign(adminInfo, result.data)
    }
  } catch (error) {
    console.error('加载管理员信息失败:', error)
  }
}

// 加载通知
async function loadNotifications() {
  try {
    const result = await admin.getNotifications()
    if (result.code === 200) {
      notifications.value = result.data.list
      notificationCount.value = result.data.unreadCount
    }
  } catch (error) {
    console.error('加载通知失败:', error)
  }
}

// 切换侧边栏
function toggleSidebar() {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 处理菜单选择
function handleMenuSelect(index) {
  router.push(index)
}

// 处理用户命令
async function handleUserCommand(command) {
  switch (command) {
    case 'profile':
      router.push('/admin/profile')
      break
    case 'logout':
      await handleLogout()
      break
  }
}

// 处理登出
async function handleLogout() {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '确认退出',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await admin.logout()
    removeToken()
    
    ElMessage.success('已退出登录')
    router.push('/admin/login')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('退出登录失败:', error)
    }
  }
}

// 标记通知为已读
async function markAsRead(notificationId) {
  try {
    const result = await admin.markNotificationRead(notificationId)
    if (result.code === 200) {
      const notification = notifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.read = true
        notificationCount.value = Math.max(0, notificationCount.value - 1)
      }
    }
  } catch (error) {
    console.error('标记通知已读失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.admin-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.admin-header {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  
  .header-left {
    .logo {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: #333;
      
      .el-icon {
        font-size: 24px;
        color: #1890ff;
      }
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 8px;
      border-radius: 6px;
      transition: background 0.3s;
      
      &:hover {
        background: #f5f5f5;
      }
      
      .username {
        font-size: 14px;
        color: #333;
      }
    }
  }
}

.admin-main {
  flex: 1;
  display: flex;
  margin-top: 60px;
}

.admin-sidebar {
  width: 240px;
  background: white;
  border-right: 1px solid #e4e7ed;
  transition: width 0.3s;
  
  &.collapsed {
    width: 64px;
  }
  
  .sidebar-toggle {
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #e4e7ed;
    
    .toggle-btn {
      width: 32px;
      height: 32px;
    }
  }
  
  .admin-menu {
    border: none;
    height: calc(100vh - 108px);
    overflow-y: auto;
    
    .el-menu-item,
    .el-sub-menu__title {
      height: 48px;
      line-height: 48px;
      
      &:hover {
        background: #f0f9ff;
      }
      
      &.is-active {
        background: #1890ff;
        color: white;
      }
    }
  }
}

.admin-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
  
  .content-header {
    height: 48px;
    background: white;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    padding: 0 20px;
  }
  
  .content-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }
}

.notifications-list {
  .notification-item {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    
    &.unread {
      background: #f0f9ff;
      border-left: 3px solid #1890ff;
    }
    
    &:last-child {
      border-bottom: none;
    }
    
    .notification-content {
      h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }
      
      p {
        margin: 0 0 8px 0;
        font-size: 13px;
        color: #666;
        line-height: 1.5;
      }
      
      .notification-time {
        font-size: 12px;
        color: #999;
      }
    }
    
    .notification-actions {
      margin-top: 8px;
    }
  }
  
  .empty-notifications {
    text-align: center;
    padding: 40px 20px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .admin-header {
    padding: 0 16px;
    
    .header-left .logo {
      font-size: 16px;
      
      span {
        display: none;
      }
    }
    
    .header-right {
      gap: 12px;
      
      .user-info .username {
        display: none;
      }
    }
  }
  
  .admin-sidebar {
    width: 64px;
    
    &.collapsed {
      width: 0;
      overflow: hidden;
    }
  }
  
  .admin-content {
    .content-header {
      padding: 0 16px;
    }
    
    .content-body {
      padding: 16px;
    }
  }
}
</style>
