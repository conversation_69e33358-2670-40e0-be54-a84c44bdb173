<template>
  <div class="content-list">
    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入内容标题"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="分类">
          <el-select v-model="searchForm.categoryId" placeholder="选择分类" clearable>
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="类型">
          <el-select v-model="searchForm.contentType" placeholder="选择类型" clearable>
            <el-option label="文章" value="article" />
            <el-option label="网盘资源" value="netdisk" />
            <el-option label="视频课程" value="video" />
            <el-option label="文档资料" value="document" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="解锁方式">
          <el-select v-model="searchForm.unlockType" placeholder="选择解锁方式" clearable>
            <el-option label="免费" value="free" />
            <el-option label="积分解锁" value="points" />
            <el-option label="VIP专享" value="vip" />
            <el-option label="卡密解锁" value="card" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="已发布" value="published" />
            <el-option label="草稿" value="draft" />
            <el-option label="已下架" value="archived" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="$router.push('/admin/content/create')">
            <el-icon><Plus /></el-icon>
            发布内容
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 内容列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="contentList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="封面" width="100">
          <template #default="{ row }">
            <el-image
              v-if="row.coverImage"
              :src="row.coverImage"
              :preview-src-list="[row.coverImage]"
              class="cover-image"
            />
            <div v-else class="no-cover">无封面</div>
          </template>
        </el-table-column>
        
        <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip />
        
        <el-table-column label="分类" width="100">
          <template #default="{ row }">
            {{ row.category?.name || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getContentTypeTagType(row.contentType)" size="small">
              {{ getContentTypeText(row.contentType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="解锁方式" width="100">
          <template #default="{ row }">
            <el-tag :type="getUnlockTypeTagType(row.unlockType)" size="small">
              {{ getUnlockTypeText(row.unlockType, row.unlockPrice) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="统计" width="120">
          <template #default="{ row }">
            <div class="stats">
              <div>浏览: {{ row.viewCount }}</div>
              <div>解锁: {{ row.unlockCount }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button text type="primary" @click="handleView(row)">
              查看
            </el-button>
            <el-button text type="primary" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-dropdown @command="(command) => handleCommand(command, row)">
              <el-button text type="primary">
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item 
                    v-if="row.status === 'draft'" 
                    command="publish"
                  >
                    发布
                  </el-dropdown-item>
                  <el-dropdown-item 
                    v-if="row.status === 'published'" 
                    command="archive"
                  >
                    下架
                  </el-dropdown-item>
                  <el-dropdown-item 
                    v-if="row.status === 'archived'" 
                    command="publish"
                  >
                    重新发布
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div v-if="selectedItems.length > 0" class="batch-actions">
        <span>已选择 {{ selectedItems.length }} 项</span>
        <el-button type="primary" @click="handleBatchPublish">批量发布</el-button>
        <el-button @click="handleBatchArchive">批量下架</el-button>
        <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { admin } from '@/api'
import { formatTime } from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  ArrowDown
} from '@element-plus/icons-vue'

const router = useRouter()

// 状态
const loading = ref(false)
const contentList = ref([])
const categories = ref([])
const selectedItems = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  title: '',
  categoryId: '',
  contentType: '',
  unlockType: '',
  status: ''
})

// 初始化
onMounted(() => {
  loadCategories()
  loadContentList()
})

// 加载分类列表
async function loadCategories() {
  try {
    const result = await admin.getCategories()
    if (result.code === 200) {
      categories.value = result.data
    }
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

// 加载内容列表
async function loadContentList() {
  try {
    loading.value = true
    
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      ...searchForm
    }
    
    const result = await admin.getContentList(params)
    if (result.code === 200) {
      contentList.value = result.data.list
      total.value = result.data.total
    }
  } catch (error) {
    console.error('加载内容列表失败:', error)
    ElMessage.error('加载内容列表失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索
function handleSearch() {
  currentPage.value = 1
  loadContentList()
}

// 处理重置
function handleReset() {
  Object.assign(searchForm, {
    title: '',
    categoryId: '',
    contentType: '',
    unlockType: '',
    status: ''
  })
  currentPage.value = 1
  loadContentList()
}

// 处理选择变化
function handleSelectionChange(selection) {
  selectedItems.value = selection
}

// 处理页码变化
function handlePageChange(page) {
  currentPage.value = page
  loadContentList()
}

// 处理页面大小变化
function handlePageSizeChange(size) {
  pageSize.value = size
  currentPage.value = 1
  loadContentList()
}

// 查看内容
function handleView(row) {
  window.open(`/content/${row.id}`, '_blank')
}

// 编辑内容
function handleEdit(row) {
  router.push(`/admin/content/edit/${row.id}`)
}

// 处理命令
async function handleCommand(command, row) {
  switch (command) {
    case 'publish':
      await handlePublish(row)
      break
    case 'archive':
      await handleArchive(row)
      break
    case 'delete':
      await handleDelete(row)
      break
  }
}

// 发布内容
async function handlePublish(row) {
  try {
    await admin.publishContent(row.id)
    ElMessage.success('发布成功')
    loadContentList()
  } catch (error) {
    console.error('发布失败:', error)
    ElMessage.error('发布失败')
  }
}

// 下架内容
async function handleArchive(row) {
  try {
    await ElMessageBox.confirm('确定要下架此内容吗？', '确认下架', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await admin.archiveContent(row.id)
    ElMessage.success('下架成功')
    loadContentList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('下架失败:', error)
      ElMessage.error('下架失败')
    }
  }
}

// 删除内容
async function handleDelete(row) {
  try {
    await ElMessageBox.confirm('确定要删除此内容吗？删除后无法恢复！', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await admin.deleteContent(row.id)
    ElMessage.success('删除成功')
    loadContentList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量发布
async function handleBatchPublish() {
  try {
    const ids = selectedItems.value.map(item => item.id)
    await admin.batchPublishContent(ids)
    ElMessage.success('批量发布成功')
    selectedItems.value = []
    loadContentList()
  } catch (error) {
    console.error('批量发布失败:', error)
    ElMessage.error('批量发布失败')
  }
}

// 批量下架
async function handleBatchArchive() {
  try {
    const ids = selectedItems.value.map(item => item.id)
    await admin.batchArchiveContent(ids)
    ElMessage.success('批量下架成功')
    selectedItems.value = []
    loadContentList()
  } catch (error) {
    console.error('批量下架失败:', error)
    ElMessage.error('批量下架失败')
  }
}

// 批量删除
async function handleBatchDelete() {
  try {
    await ElMessageBox.confirm('确定要删除选中的内容吗？删除后无法恢复！', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const ids = selectedItems.value.map(item => item.id)
    await admin.batchDeleteContent(ids)
    ElMessage.success('批量删除成功')
    selectedItems.value = []
    loadContentList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 获取内容类型文本
function getContentTypeText(type) {
  const typeMap = {
    article: '文章',
    netdisk: '网盘',
    video: '视频',
    document: '文档'
  }
  return typeMap[type] || '文章'
}

// 获取内容类型标签类型
function getContentTypeTagType(type) {
  const typeMap = {
    article: 'primary',
    netdisk: 'success',
    video: 'warning',
    document: 'info'
  }
  return typeMap[type] || 'primary'
}

// 获取解锁类型文本
function getUnlockTypeText(type, price) {
  if (type === 'free') return '免费'
  if (type === 'vip') return 'VIP'
  if (type === 'points') return `${price}积分`
  if (type === 'card') return '卡密'
  return '免费'
}

// 获取解锁类型标签类型
function getUnlockTypeTagType(type) {
  const typeMap = {
    free: 'success',
    vip: 'warning',
    points: 'primary',
    card: 'info'
  }
  return typeMap[type] || 'success'
}

// 获取状态文本
function getStatusText(status) {
  const statusMap = {
    published: '已发布',
    draft: '草稿',
    archived: '已下架'
  }
  return statusMap[status] || '草稿'
}

// 获取状态标签类型
function getStatusTagType(status) {
  const typeMap = {
    published: 'success',
    draft: 'info',
    archived: 'warning'
  }
  return typeMap[status] || 'info'
}
</script>

<style lang="scss" scoped>
.content-list {
  .search-card {
    margin-bottom: 20px;
  }
  
  .table-card {
    .cover-image {
      width: 60px;
      height: 40px;
      border-radius: 4px;
    }
    
    .no-cover {
      width: 60px;
      height: 40px;
      background: #f5f5f5;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #999;
    }
    
    .stats {
      font-size: 12px;
      color: #666;
      
      div {
        margin-bottom: 2px;
      }
    }
    
    .batch-actions {
      margin: 16px 0;
      padding: 12px;
      background: #f0f9ff;
      border-radius: 6px;
      display: flex;
      align-items: center;
      gap: 12px;
      
      span {
        color: #1890ff;
        font-weight: 500;
      }
    }
    
    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
