<template>
  <div class="privacy-page">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>隐私政策</h1>
        <p class="update-time">最后更新时间：2023年12月</p>
      </div>

      <!-- 政策内容 -->
      <div class="policy-content">
        <section>
          <h2>1. 信息收集</h2>
          <p>我们可能收集以下类型的信息：</p>
          <ul>
            <li><strong>个人信息：</strong>包括但不限于姓名、手机号码、邮箱地址等</li>
            <li><strong>微信信息：</strong>微信授权提供的昵称、头像、openid等</li>
            <li><strong>使用信息：</strong>您在使用我们服务时产生的行为数据</li>
            <li><strong>设备信息：</strong>设备型号、操作系统、IP地址等技术信息</li>
          </ul>
        </section>

        <section>
          <h2>2. 信息使用</h2>
          <p>我们收集的信息将用于：</p>
          <ul>
            <li>提供、维护和改进我们的服务</li>
            <li>处理您的请求和交易</li>
            <li>发送重要通知和更新</li>
            <li>个性化内容推荐</li>
            <li>防范欺诈和滥用行为</li>
          </ul>
        </section>

        <section>
          <h2>3. 信息分享</h2>
          <p>我们不会出售、交易或转让您的个人信息给第三方，除非：</p>
          <ul>
            <li>获得您的明确同意</li>
            <li>法律法规要求</li>
            <li>保护我们的权利和财产</li>
            <li>与可信的第三方服务提供商合作（他们同意保密）</li>
          </ul>
        </section>

        <section>
          <h2>4. 信息安全</h2>
          <p>我们采取多种安全措施保护您的个人信息：</p>
          <ul>
            <li>数据加密传输和存储</li>
            <li>访问控制和权限管理</li>
            <li>定期安全审计和漏洞修复</li>
            <li>员工隐私培训和保密协议</li>
          </ul>
        </section>

        <section>
          <h2>5. Cookie使用</h2>
          <p>我们使用Cookie和类似技术来：</p>
          <ul>
            <li>记住您的登录状态</li>
            <li>分析网站使用情况</li>
            <li>提供个性化体验</li>
            <li>改进服务质量</li>
          </ul>
          <p>您可以通过浏览器设置管理Cookie偏好。</p>
        </section>

        <section>
          <h2>6. 您的权利</h2>
          <p>您对个人信息享有以下权利：</p>
          <ul>
            <li><strong>访问权：</strong>查看我们持有的您的个人信息</li>
            <li><strong>更正权：</strong>更新或修正不准确的信息</li>
            <li><strong>删除权：</strong>要求删除您的个人信息</li>
            <li><strong>限制权：</strong>限制我们处理您的信息</li>
            <li><strong>反对权：</strong>反对我们处理您的信息</li>
          </ul>
        </section>

        <section>
          <h2>7. 儿童隐私</h2>
          <p>
            我们的服务不面向13岁以下的儿童。如果我们发现收集了儿童的个人信息，
            我们将立即删除这些信息。如果您是家长或监护人，发现您的孩子向我们
            提供了个人信息，请联系我们。
          </p>
        </section>

        <section>
          <h2>8. 政策更新</h2>
          <p>
            我们可能会不时更新本隐私政策。重大变更时，我们会通过网站公告或
            其他方式通知您。建议您定期查看本政策以了解最新信息。
          </p>
        </section>

        <section>
          <h2>9. 联系我们</h2>
          <p>如果您对本隐私政策有任何疑问或建议，请通过以下方式联系我们：</p>
          <div class="contact-info">
            <p><strong>邮箱：</strong><EMAIL></p>
            <p><strong>电话：</strong>400-123-4567</p>
            <p><strong>地址：</strong>北京市朝阳区xxx街道xxx号</p>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<script setup>
// 页面元数据
document.title = '隐私政策 - 知识付费平台'
</script>

<style lang="scss" scoped>
.privacy-page {
  min-height: 100vh;
  background: #f8fafc;
  padding: 40px 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  
  h1 {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
  }
  
  .update-time {
    font-size: 14px;
    color: #666;
    margin: 0;
  }
}

.policy-content {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  
  section {
    margin-bottom: 32px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    h2 {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #1890ff;
    }
    
    p {
      font-size: 15px;
      line-height: 1.7;
      color: #555;
      margin: 0 0 12px 0;
    }
    
    ul {
      margin: 12px 0;
      padding-left: 20px;
      
      li {
        font-size: 15px;
        line-height: 1.7;
        color: #555;
        margin-bottom: 8px;
        
        strong {
          color: #333;
        }
      }
    }
    
    .contact-info {
      background: #f8fafc;
      padding: 20px;
      border-radius: 8px;
      margin-top: 16px;
      
      p {
        margin: 0 0 8px 0;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        strong {
          color: #333;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .privacy-page {
    padding: 20px 0;
  }
  
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      font-size: 24px;
    }
  }
  
  .policy-content {
    padding: 24px 20px;
    
    section {
      margin-bottom: 24px;
      
      h2 {
        font-size: 18px;
      }
      
      p, li {
        font-size: 14px;
      }
    }
  }
}
</style>
