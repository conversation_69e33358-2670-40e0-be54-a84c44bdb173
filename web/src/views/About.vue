<template>
  <div class="about-page">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>关于我们</h1>
        <p>了解知识付费平台的愿景与使命</p>
      </div>

      <!-- 平台介绍 -->
      <section class="platform-intro">
        <div class="intro-content">
          <h2>平台简介</h2>
          <p>
            知识付费平台致力于打造一个覆盖考试与自学用户、支持微信与网页访问、
            具有积分激励与传播裂变能力的内容分发平台。我们提供高质量的学习资源，
            建立完善的积分奖励机制，构建可持续增长的用户生态体系。
          </p>
        </div>
        <div class="intro-image">
          <el-icon class="icon"><Reading /></el-icon>
        </div>
      </section>

      <!-- 核心优势 -->
      <section class="advantages">
        <h2>核心优势</h2>
        <div class="advantages-grid">
          <div class="advantage-item">
            <el-icon class="icon"><Monitor /></el-icon>
            <h3>多端统一</h3>
            <p>微信生态 + 网页端无缝切换，账号数据实时同步</p>
          </div>
          <div class="advantage-item">
            <el-icon class="icon"><Trophy /></el-icon>
            <h3>激励体系</h3>
            <p>积分、VIP、邀请多重激励机制，提升用户活跃度</p>
          </div>
          <div class="advantage-item">
            <el-icon class="icon"><Document /></el-icon>
            <h3>内容丰富</h3>
            <p>文章 + 网盘资源多样化内容形式，满足不同需求</p>
          </div>
          <div class="advantage-item">
            <el-icon class="icon"><Share /></el-icon>
            <h3>社交裂变</h3>
            <p>基于微信生态的天然传播优势，快速用户增长</p>
          </div>
        </div>
      </section>

      <!-- 目标用户 -->
      <section class="target-users">
        <h2>目标用户</h2>
        <div class="users-grid">
          <div class="user-type">
            <el-icon class="icon"><EditPen /></el-icon>
            <h3>应试类用户</h3>
            <p>考研、考证、公务员考试人群</p>
            <ul>
              <li>高质量备考资料</li>
              <li>真题解析</li>
              <li>学习计划</li>
            </ul>
          </div>
          <div class="user-type">
            <el-icon class="icon"><User /></el-icon>
            <h3>自学用户</h3>
            <p>有技能提升或知识兴趣的个人</p>
            <ul>
              <li>系统化课程</li>
              <li>实用技能</li>
              <li>兴趣拓展</li>
            </ul>
          </div>
          <div class="user-type">
            <el-icon class="icon"><UserFilled /></el-icon>
            <h3>社群分发者</h3>
            <p>有资源或流量，愿意推广分享的用户</p>
            <ul>
              <li>分享激励</li>
              <li>佣金收益</li>
              <li>推广工具</li>
            </ul>
          </div>
        </div>
      </section>

      <!-- 联系我们 -->
      <section class="contact">
        <h2>联系我们</h2>
        <div class="contact-info">
          <div class="contact-item">
            <el-icon><Message /></el-icon>
            <span>客服邮箱：<EMAIL></span>
          </div>
          <div class="contact-item">
            <el-icon><Phone /></el-icon>
            <span>客服电话：************</span>
          </div>
          <div class="contact-item">
            <el-icon><Clock /></el-icon>
            <span>服务时间：周一至周五 9:00-18:00</span>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import {
  Reading,
  Monitor,
  Trophy,
  Document,
  Share,
  EditPen,
  User,
  UserFilled,
  Message,
  Phone,
  Clock
} from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.about-page {
  min-height: 100vh;
  background: #f8fafc;
  padding: 40px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 60px;
  
  h1 {
    font-size: 36px;
    font-weight: 600;
    color: #333;
    margin: 0 0 16px 0;
  }
  
  p {
    font-size: 18px;
    color: #666;
    margin: 0;
  }
}

section {
  margin-bottom: 60px;
  
  h2 {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin: 0 0 32px 0;
    text-align: center;
  }
}

.platform-intro {
  display: flex;
  align-items: center;
  gap: 60px;
  background: white;
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  
  .intro-content {
    flex: 1;
    
    h2 {
      text-align: left;
      margin-bottom: 24px;
    }
    
    p {
      font-size: 16px;
      line-height: 1.8;
      color: #555;
      margin: 0;
    }
  }
  
  .intro-image {
    flex-shrink: 0;
    
    .icon {
      font-size: 120px;
      color: #1890ff;
    }
  }
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 32px;
  
  .advantage-item {
    background: white;
    padding: 32px 24px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s;
    
    &:hover {
      transform: translateY(-4px);
    }
    
    .icon {
      font-size: 48px;
      color: #1890ff;
      margin-bottom: 16px;
    }
    
    h3 {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin: 0 0 12px 0;
    }
    
    p {
      font-size: 14px;
      color: #666;
      line-height: 1.6;
      margin: 0;
    }
  }
}

.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  
  .user-type {
    background: white;
    padding: 32px 24px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    
    .icon {
      font-size: 48px;
      color: #1890ff;
      margin-bottom: 16px;
    }
    
    h3 {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }
    
    p {
      font-size: 14px;
      color: #666;
      margin: 0 0 16px 0;
    }
    
    ul {
      list-style: none;
      padding: 0;
      margin: 0;
      
      li {
        font-size: 14px;
        color: #555;
        padding: 4px 0;
        position: relative;
        padding-left: 16px;
        
        &::before {
          content: '•';
          color: #1890ff;
          position: absolute;
          left: 0;
        }
      }
    }
  }
}

.contact {
  background: white;
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  
  .contact-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-width: 400px;
    margin: 0 auto;
    
    .contact-item {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 16px;
      color: #555;
      
      .el-icon {
        color: #1890ff;
        font-size: 20px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .about-page {
    padding: 20px 0;
  }
  
  .page-header {
    margin-bottom: 40px;
    
    h1 {
      font-size: 28px;
    }
    
    p {
      font-size: 16px;
    }
  }
  
  section {
    margin-bottom: 40px;
    
    h2 {
      font-size: 24px;
      margin-bottom: 24px;
    }
  }
  
  .platform-intro {
    flex-direction: column;
    text-align: center;
    padding: 24px;
    gap: 32px;
    
    .intro-image .icon {
      font-size: 80px;
    }
  }
  
  .advantages-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    
    .advantage-item {
      padding: 24px 20px;
    }
  }
  
  .users-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    
    .user-type {
      padding: 24px 20px;
    }
  }
  
  .contact {
    padding: 24px;
    
    .contact-info {
      .contact-item {
        font-size: 14px;
      }
    }
  }
}
</style>
