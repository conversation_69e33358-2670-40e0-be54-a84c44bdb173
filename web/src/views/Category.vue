<template>
  <div class="category-page">
    <!-- 顶部导航 -->
    <div class="page-header">
      <div class="container">
        <div class="header-content">
          <h1>内容分类</h1>
          <p>发现更多精彩内容</p>
        </div>
      </div>
    </div>

    <div class="container">
      <!-- 分类导航 -->
      <div class="category-nav">
        <div class="nav-scroll">
          <div 
            v-for="category in categories" 
            :key="category.id"
            :class="['nav-item', { active: selectedCategory === category.id }]"
            @click="selectCategory(category.id)"
          >
            <span class="icon">{{ category.icon }}</span>
            <span class="name">{{ category.name }}</span>
            <span class="count">({{ category.contentCount }})</span>
          </div>
        </div>
      </div>

      <!-- 筛选和排序 -->
      <div class="filter-bar">
        <div class="filter-left">
          <!-- 内容类型筛选 -->
          <el-select 
            v-model="filters.contentType" 
            placeholder="内容类型"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="全部类型" value="" />
            <el-option label="文章" value="article" />
            <el-option label="网盘资源" value="netdisk" />
            <el-option label="视频课程" value="video" />
            <el-option label="文档资料" value="document" />
          </el-select>

          <!-- 解锁方式筛选 -->
          <el-select 
            v-model="filters.unlockType" 
            placeholder="解锁方式"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="全部方式" value="" />
            <el-option label="免费内容" value="free" />
            <el-option label="积分解锁" value="points" />
            <el-option label="VIP专享" value="vip" />
            <el-option label="卡密解锁" value="card" />
          </el-select>

          <!-- 积分价格筛选 -->
          <el-select 
            v-model="filters.priceRange" 
            placeholder="积分价格"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="全部价格" value="" />
            <el-option label="免费" value="0" />
            <el-option label="1-10积分" value="1-10" />
            <el-option label="11-50积分" value="11-50" />
            <el-option label="51-100积分" value="51-100" />
            <el-option label="100+积分" value="100+" />
          </el-select>
        </div>

        <div class="filter-right">
          <!-- 排序方式 -->
          <el-select 
            v-model="sortBy" 
            placeholder="排序方式"
            @change="handleSortChange"
          >
            <el-option label="最新发布" value="latest" />
            <el-option label="最多浏览" value="views" />
            <el-option label="最多点赞" value="likes" />
            <el-option label="最多解锁" value="unlocks" />
            <el-option label="积分价格" value="price" />
          </el-select>

          <!-- 视图切换 -->
          <div class="view-toggle">
            <el-button-group>
              <el-button 
                :type="viewMode === 'grid' ? 'primary' : ''"
                @click="viewMode = 'grid'"
                size="small"
              >
                <el-icon><Grid /></el-icon>
              </el-button>
              <el-button 
                :type="viewMode === 'list' ? 'primary' : ''"
                @click="viewMode = 'list'"
                size="small"
              >
                <el-icon><List /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </div>

      <!-- 内容列表 -->
      <div class="content-section">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="6" animated />
        </div>

        <!-- 内容网格视图 -->
        <div v-else-if="viewMode === 'grid'" class="content-grid">
          <ContentCard 
            v-for="content in contentList" 
            :key="content.id"
            :content="content"
            @click="$router.push(`/content/${content.id}`)"
          />
        </div>

        <!-- 内容列表视图 -->
        <div v-else class="content-list">
          <ContentListItem 
            v-for="content in contentList" 
            :key="content.id"
            :content="content"
            @click="$router.push(`/content/${content.id}`)"
          />
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && contentList.length === 0" class="empty-state">
          <el-empty description="暂无相关内容">
            <el-button type="primary" @click="clearFilters">
              清除筛选条件
            </el-button>
          </el-empty>
        </div>

        <!-- 分页 -->
        <div v-if="!loading && contentList.length > 0" class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[12, 24, 48]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePageSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { content, publicApi } from '@/api'
import { ElMessage } from 'element-plus'
import { Grid, List } from '@element-plus/icons-vue'
import ContentCard from '@/components/ContentCard.vue'
import ContentListItem from '@/components/ContentListItem.vue'

const route = useRoute()
const router = useRouter()

// 状态
const loading = ref(false)
const categories = ref([])
const contentList = ref([])
const selectedCategory = ref('')
const viewMode = ref('grid')
const sortBy = ref('latest')
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)

// 筛选条件
const filters = reactive({
  contentType: '',
  unlockType: '',
  priceRange: ''
})

// 初始化
onMounted(() => {
  loadCategories()
  initFromQuery()
  loadContent()
})

// 监听路由变化
watch(() => route.query, () => {
  initFromQuery()
  loadContent()
})

// 从URL参数初始化状态
function initFromQuery() {
  selectedCategory.value = route.query.categoryId || ''
  filters.contentType = route.query.contentType || ''
  filters.unlockType = route.query.unlockType || ''
  filters.priceRange = route.query.priceRange || ''
  sortBy.value = route.query.sort || 'latest'
  viewMode.value = route.query.view || 'grid'
  currentPage.value = parseInt(route.query.page) || 1
  pageSize.value = parseInt(route.query.pageSize) || 12
}

// 加载分类列表
async function loadCategories() {
  try {
    const result = await publicApi.getCategories()
    if (result.code === 200) {
      categories.value = [
        { id: '', name: '全部分类', icon: '📚', contentCount: 0 },
        ...result.data
      ]
    }
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

// 加载内容列表
async function loadContent() {
  try {
    loading.value = true
    
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      sort: sortBy.value,
      categoryId: selectedCategory.value || undefined,
      contentType: filters.contentType || undefined,
      unlockType: filters.unlockType || undefined,
      priceRange: filters.priceRange || undefined
    }

    const result = await content.getList(params)
    if (result.code === 200) {
      contentList.value = result.data.list
      total.value = result.data.total
    }
  } catch (error) {
    console.error('加载内容失败:', error)
    ElMessage.error('加载内容失败')
  } finally {
    loading.value = false
  }
}

// 选择分类
function selectCategory(categoryId) {
  selectedCategory.value = categoryId
  currentPage.value = 1
  updateQuery()
}

// 处理筛选变化
function handleFilterChange() {
  currentPage.value = 1
  updateQuery()
}

// 处理排序变化
function handleSortChange() {
  currentPage.value = 1
  updateQuery()
}

// 处理页码变化
function handlePageChange(page) {
  currentPage.value = page
  updateQuery()
}

// 处理页面大小变化
function handlePageSizeChange(size) {
  pageSize.value = size
  currentPage.value = 1
  updateQuery()
}

// 清除筛选条件
function clearFilters() {
  selectedCategory.value = ''
  filters.contentType = ''
  filters.unlockType = ''
  filters.priceRange = ''
  sortBy.value = 'latest'
  currentPage.value = 1
  updateQuery()
}

// 更新URL参数
function updateQuery() {
  const query = {}
  
  if (selectedCategory.value) query.categoryId = selectedCategory.value
  if (filters.contentType) query.contentType = filters.contentType
  if (filters.unlockType) query.unlockType = filters.unlockType
  if (filters.priceRange) query.priceRange = filters.priceRange
  if (sortBy.value !== 'latest') query.sort = sortBy.value
  if (viewMode.value !== 'grid') query.view = viewMode.value
  if (currentPage.value > 1) query.page = currentPage.value
  if (pageSize.value !== 12) query.pageSize = pageSize.value

  router.push({ query })
}
</script>

<style lang="scss" scoped>
.category-page {
  min-height: 100vh;
  background: #f8fafc;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px 0;
  
  .header-content {
    text-align: center;
    
    h1 {
      font-size: 32px;
      font-weight: 600;
      margin: 0 0 8px 0;
    }
    
    p {
      font-size: 16px;
      margin: 0;
      opacity: 0.9;
    }
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.category-nav {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin: -20px 20px 24px 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  
  .nav-scroll {
    display: flex;
    gap: 16px;
    overflow-x: auto;
    padding-bottom: 4px;
    
    &::-webkit-scrollbar {
      height: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #ddd;
      border-radius: 2px;
    }
  }
  
  .nav-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    white-space: nowrap;
    border: 1px solid #e4e7ed;
    
    &:hover {
      background: #f0f9ff;
      border-color: #1890ff;
    }
    
    &.active {
      background: #1890ff;
      color: white;
      border-color: #1890ff;
    }
    
    .icon {
      font-size: 16px;
    }
    
    .name {
      font-weight: 500;
    }
    
    .count {
      font-size: 12px;
      opacity: 0.7;
    }
  }
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
  
  .filter-left {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }
  
  .filter-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .view-toggle {
    .el-button-group {
      .el-button {
        padding: 8px 12px;
      }
    }
  }
}

.content-section {
  .content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
  }
  
  .content-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 32px;
  }
  
  .empty-state {
    text-align: center;
    padding: 60px 20px;
  }
  
  .pagination {
    display: flex;
    justify-content: center;
    padding: 20px 0;
  }
}

.loading-container {
  padding: 20px;
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    padding: 24px 0;
    
    .header-content {
      h1 {
        font-size: 24px;
      }
      
      p {
        font-size: 14px;
      }
    }
  }
  
  .category-nav {
    margin: -12px 0 16px 0;
    padding: 16px;
    
    .nav-item {
      padding: 8px 12px;
      
      .icon {
        font-size: 14px;
      }
      
      .name {
        font-size: 14px;
      }
      
      .count {
        font-size: 11px;
      }
    }
  }
  
  .filter-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    
    .filter-left {
      order: 2;
    }
    
    .filter-right {
      order: 1;
      justify-content: space-between;
    }
  }
  
  .content-section {
    .content-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
}
</style>
